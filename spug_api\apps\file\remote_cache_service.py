# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

import logging
import time
import concurrent.futures
import threading
import os
import sys
from typing import List, Dict, Optional, Tuple, Set
from django.utils import timezone
from django.db import transaction, connection
from apps.file.models import RemoteFolder, RemoteFolderCache
from apps.file.remote_manager import RemoteFileManager
from datetime import datetime, timedelta
from django.conf import settings
import json
import sqlite3

# 设置PYTHONPATH环境变量，解决funboost导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 创建nb_log_config.py的副本到当前目录
nb_log_config_path = os.path.join(project_root, 'nb_log_config.py')
local_nb_log_config_path = os.path.join(os.path.dirname(project_root), 'nb_log_config.py')
if not os.path.exists(local_nb_log_config_path) and os.path.exists(nb_log_config_path):
    try:
        import shutil
        shutil.copy(nb_log_config_path, os.path.dirname(project_root))
        logging.info(f"已复制nb_log_config.py到{os.path.dirname(project_root)}")
    except Exception as e:
        logging.warning(f"复制nb_log_config.py失败: {str(e)}")

# 检查funboost是否可用
FUNBOOST_AVAILABLE = False
try:
    import funboost
    from funboost import boost, BrokerEnum, ConcurrentModeEnum, BoosterParams
    FUNBOOST_AVAILABLE = True
    logging.info("funboost已加载，将使用分布式加速方案")
except ImportError:
    logging.warning("未安装funboost，将使用内置多线程方案")
except Exception as e:
    logging.warning(f"funboost导入失败: {e}，将使用内置多线程方案")

logger = logging.getLogger(__name__)

class RemoteFolderCacheService:
    """远程文件夹缓存服务"""
    
    def __init__(self):
        self.lock = threading.Lock()
        self.cache_expire_minutes = getattr(settings, 'REMOTE_FOLDER_CACHE_EXPIRE_MINUTES', 30)
        self.max_depth = 3  # 最大递归深度，防止无限递归
        self.max_workers = 3  # 最大线程数，减少以避免数据库锁定
        self.thread_local = threading.local()  # 线程本地存储
        
        # 初始化funboost任务（如果可用）
        global FUNBOOST_AVAILABLE
        if FUNBOOST_AVAILABLE:
            try:
                self._init_funboost_tasks()
                logger.info("funboost多线程加速已启用")
            except Exception as e:
                logger.error(f"funboost初始化失败: {str(e)}")
                FUNBOOST_AVAILABLE = False
        
    def _init_funboost_tasks(self):
        """初始化funboost任务"""
        # 定义funboost参数
        class CacheBoosterParams(BoosterParams):
            broker_kind = BrokerEnum.MEMORY_QUEUE  # 使用内存队列作为中间件
            concurrent_mode = ConcurrentModeEnum.THREADING  # 使用线程模式
            qps = 100  # 每秒处理100个任务
            concurrent_num = self.max_workers  # 并发数
            
        # 定义缓存文件夹任务
        @boost(CacheBoosterParams(queue_name='remote_folder_cache'))
        def cache_folder_task(remote_folder_id, path, depth=0):
            """缓存单个文件夹的任务"""
            try:
                # 如果超过最大深度，直接返回
                if depth > self.max_depth:
                    logger.warning(f"达到最大递归深度 {self.max_depth}: 路径={path}, 文件夹ID={remote_folder_id}")
                    return {'status': 'skip', 'reason': 'max_depth_reached'}
                
                # 获取远程文件夹
                remote_folder = RemoteFolder.objects.get(id=remote_folder_id, is_active=True)
                
                # 获取并缓存文件列表
                files = self._fetch_and_cache_files(remote_folder, path)
                
                # 如果获取失败，直接返回
                if not files:
                    return {'status': 'failed', 'path': path}
                
                # 递归处理子文件夹
                for file_item in files:
                    if file_item.get('type') == 'folder':
                        subfolder_path = f"{path}/{file_item['name']}" if path else file_item['name']
                        # 推送子文件夹任务到队列
                        cache_folder_task.push(remote_folder_id=remote_folder_id, path=subfolder_path, depth=depth+1)
                
                return {'status': 'success', 'path': path, 'file_count': len(files)}
                
            except Exception as e:
                logger.error(f"缓存任务异常: {str(e)}, 路径={path}, 文件夹ID={remote_folder_id}")
                return {'status': 'error', 'error': str(e), 'path': path}
        
        # 保存任务引用
        self.cache_folder_task = cache_folder_task
        
    def get_cached_files(self, folder_id, path=""):
        """获取缓存的文件列表，如果缓存不存在或已过期则返回None"""
        try:
            # 查询缓存
            cache = RemoteFolderCache.objects.filter(
                remote_folder_id=folder_id,
                path=path
            ).first()
            
            # 如果缓存存在且有效
            if cache and not cache.is_expired(self.cache_expire_minutes):
                files = cache.get_files()
                return files, True
            
            # 缓存不存在或已过期，尝试获取远程文件列表
            folder = RemoteFolder.objects.get(id=folder_id, is_active=True)
            remote_manager = RemoteFileManager(
                folder.remote_path,
                folder.username,
                folder.password,
                folder.domain
            )
            
            if not remote_manager.connect():
                return [], False
            
            try:
                files = remote_manager.list_files(path)
                
                # 更新或创建缓存
                with transaction.atomic():
                    if cache:
                        cache.set_files(files)
                        cache.save()
                    else:
                        RemoteFolderCache.objects.create(
                            remote_folder_id=folder_id,
                            path=path,
                            files_data=json.dumps(files, ensure_ascii=False)
                        )
                
                return files, False
            finally:
                remote_manager.disconnect()
                
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return [], False

    def refresh_cache(self, folder_id, path=""):
        """刷新缓存"""
        try:
            folder = RemoteFolder.objects.get(id=folder_id, is_active=True)
            remote_manager = RemoteFileManager(
                folder.remote_path,
                folder.username,
                folder.password,
                folder.domain
            )
            
            if not remote_manager.connect():
                return False
            
            try:
                files = remote_manager.list_files(path)
                
                # 使用数据库连接池和重试机制来避免锁定问题
                max_retries = 3
                retry_delay = 0.1  # 100ms
                
                for attempt in range(max_retries):
                    try:
                        # 更新或创建缓存，使用较短的事务
                        with transaction.atomic():
                            cache, created = RemoteFolderCache.objects.get_or_create(
                                remote_folder_id=folder_id,
                                path=path,
                                defaults={'files_data': json.dumps(files, ensure_ascii=False)}
                            )
                            
                            if not created:
                                cache.set_files(files)
                                cache.save()
                        
                        return True
                    except Exception as db_e:
                        if 'database is locked' in str(db_e) and attempt < max_retries - 1:
                            # 数据库锁定，等待后重试
                            import time
                            time.sleep(retry_delay * (attempt + 1))  # 指数退避
                            continue
                        else:
                            raise db_e
                
                return False
                
            finally:
                remote_manager.disconnect()
                
        except Exception as e:
            logger.error(f"刷新缓存失败: {e}")
            return False
            
    def refresh_all_caches(self):
        """刷新所有过期的缓存"""
        try:
            # 获取所有过期的缓存
            expired_time = timezone.now() - timedelta(minutes=self.cache_expire_minutes)
            expired_caches = RemoteFolderCache.objects.filter(
                cache_time__lt=expired_time
            )
            
            if not expired_caches:
                return True
                
            # 减少并发数量以避免数据库锁定问题
            max_workers = min(3, len(expired_caches))  # 限制最大3个线程
            
            # 使用有限制的多线程刷新缓存
            global FUNBOOST_AVAILABLE
            if FUNBOOST_AVAILABLE:
                # 使用funboost
                from concurrent.futures import ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = []
                    for cache in expired_caches:
                        future = executor.submit(self.refresh_cache, cache.remote_folder.id, cache.path)
                        futures.append(future)
                    
                    # 等待所有任务完成
                    for future in futures:
                        try:
                            future.result(timeout=30)  # 30秒超时
                        except Exception as e:
                            logger.error(f"缓存刷新任务失败: {e}")
            else:
                # 使用内置多线程，但限制并发数
                import time
                cache_list = list(expired_caches)
                
                # 分批处理，避免过多并发
                batch_size = max_workers
                for i in range(0, len(cache_list), batch_size):
                    batch = cache_list[i:i + batch_size]
                    threads = []
                    
                    for cache in batch:
                        thread = threading.Thread(
                            target=self.refresh_cache,
                            args=(cache.remote_folder.id, cache.path)
                        )
                        thread.daemon = True
                        threads.append(thread)
                        thread.start()
                    
                    # 等待当前批次完成
                    for thread in threads:
                        thread.join()
                    
                    # 批次间短暂延迟，减少数据库压力
                    if i + batch_size < len(cache_list):
                        time.sleep(0.1)
            
            return True
        except Exception as e:
            logger.error(f"刷新所有缓存失败: {e}")
            return False
            
    def clear_cache(self, folder_id=None, path=None):
        """清除缓存"""
        try:
            if folder_id and path:
                # 清除指定缓存
                RemoteFolderCache.objects.filter(
                    remote_folder_id=folder_id,
                    path=path
                ).delete()
            elif folder_id:
                # 清除指定文件夹的所有缓存
                RemoteFolderCache.objects.filter(
                    remote_folder_id=folder_id
                ).delete()
            else:
                # 清除所有缓存
                RemoteFolderCache.objects.all().delete()
            return True
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
            return False

    def cache_entire_file_tree(self, folder_id):
        """缓存整个文件树"""
        try:
            # 获取远程文件夹配置
            folder = RemoteFolder.objects.get(id=folder_id, is_active=True)
            logger.info(f"开始缓存整个文件树: {folder.name} (ID: {folder_id})")
            
            # 初始化统计信息
            stats = {
                'folder_id': folder_id,
                'folder_name': folder.name,
                'total_processed': 0,
                'success_count': 0,
                'error_count': 0,
                'skipped_count': 0,
                'start_time': timezone.now(),
                'end_time': None,
                'errors': []
            }
            
            # 使用队列来管理要处理的目录
            directories_to_process = ['']  # 从根目录开始
            processed_directories = set()
            
            while directories_to_process:
                current_path = directories_to_process.pop(0)
                
                # 避免重复处理
                if current_path in processed_directories:
                    continue
                processed_directories.add(current_path)
                
                try:
                    # 刷新当前目录的缓存
                    success = self.refresh_cache(folder_id, current_path)
                    stats['total_processed'] += 1
                    
                    if success:
                        stats['success_count'] += 1
                        
                        # 获取当前目录的文件列表
                        files, _ = self.get_cached_files(folder_id, current_path)
                        
                        # 将子目录添加到处理队列
                        for file_item in files:
                            if file_item.get('type') == 'folder':
                                subfolder_path = f"{current_path}/{file_item['name']}" if current_path else file_item['name']
                                if subfolder_path not in processed_directories:
                                    directories_to_process.append(subfolder_path)
                    else:
                        stats['error_count'] += 1
                        stats['errors'].append(f"无法缓存路径: {current_path}")
                        
                except Exception as e:
                    stats['error_count'] += 1
                    error_msg = f"处理路径 {current_path} 时出错: {str(e)}"
                    stats['errors'].append(error_msg)
                    logger.error(error_msg)
                
                # 限制处理的目录数量，避免无限递归
                if stats['total_processed'] >= 1000:
                    stats['skipped_count'] = len(directories_to_process)
                    logger.warning(f"达到最大处理数量限制，跳过剩余 {stats['skipped_count']} 个目录")
                    break
            
            stats['end_time'] = timezone.now()
            duration = (stats['end_time'] - stats['start_time']).total_seconds()
            
            logger.info(f"文件树缓存完成: 处理 {stats['total_processed']} 个目录, "
                       f"成功 {stats['success_count']}, 失败 {stats['error_count']}, "
                       f"用时 {duration:.2f} 秒")
            
            return stats
            
        except RemoteFolder.DoesNotExist:
            logger.error(f"远程文件夹不存在: {folder_id}")
            return {'error': f'远程文件夹不存在: {folder_id}'}
        except Exception as e:
            logger.error(f"缓存整个文件树失败: {e}")
            return {'error': str(e)}

    def refresh_folder_cache(self, folder_id, path="", recursive=False):
        """刷新文件夹缓存"""
        try:
            if recursive:
                # 递归刷新，使用cache_entire_file_tree
                stats = self.cache_entire_file_tree(folder_id)
                return 'error' not in stats
            else:
                # 只刷新单个路径
                return self.refresh_cache(folder_id, path)
        except Exception as e:
            logger.error(f"刷新文件夹缓存失败: {e}")
            return False

    def get_cache_status(self):
        """获取缓存状态"""
        try:
            # 统计所有远程文件夹的缓存状态
            folders = RemoteFolder.objects.filter(is_active=True)
            
            status = {
                'total_folders': folders.count(),
                'folders_with_cache': 0,
                'total_cache_entries': 0,
                'cache_size_mb': 0,
                'folders': []
            }
            
            for folder in folders:
                folder_caches = RemoteFolderCache.objects.filter(remote_folder=folder)
                cache_count = folder_caches.count()
                
                if cache_count > 0:
                    status['folders_with_cache'] += 1
                
                status['total_cache_entries'] += cache_count
                
                # 计算缓存大小（粗略估算）
                total_size = 0
                for cache in folder_caches:
                    total_size += len(cache.files_data.encode('utf-8'))
                
                folder_status = {
                    'id': folder.id,
                    'name': folder.name,
                    'cache_entries': cache_count,
                    'cache_size_bytes': total_size,
                    'cache_size_kb': round(total_size / 1024, 2) if total_size > 0 else 0,
                    'last_cached': None
                }
                
                # 获取最近的缓存时间
                if cache_count > 0:
                    latest_cache = folder_caches.order_by('-cache_time').first()
                    if latest_cache and latest_cache.cache_time:
                        folder_status['last_cached'] = latest_cache.cache_time.strftime('%Y-%m-%d %H:%M:%S')
                
                status['folders'].append(folder_status)
                status['cache_size_mb'] += total_size
            
            # 转换总大小为MB
            status['cache_size_mb'] = round(status['cache_size_mb'] / (1024 * 1024), 2)
            
            return status
            
        except Exception as e:
            logger.error(f"获取缓存状态失败: {e}")
            return {'error': str(e)}

# 延迟创建缓存服务实例
_cache_service_instance = None

def get_cache_service():
    """获取缓存服务实例（延迟初始化）"""
    global _cache_service_instance
    if _cache_service_instance is None:
        _cache_service_instance = RemoteFolderCacheService()
    return _cache_service_instance

# 为了保持向后兼容，创建一个属性
class CacheServiceProxy:
    def __getattr__(self, name):
        return getattr(get_cache_service(), name)

cache_service = CacheServiceProxy() 