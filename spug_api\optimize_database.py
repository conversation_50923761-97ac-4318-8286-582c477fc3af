#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库优化脚本
"""

import os
import sys
import sqlite3
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def optimize_sqlite_database():
    """优化SQLite数据库"""
    db_path = os.path.join(os.path.dirname(__file__), 'db.sqlite3')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    print(f"优化数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()
        
        print("执行数据库优化...")
        
        # 设置WAL模式（Write-Ahead Logging）
        cursor.execute("PRAGMA journal_mode=WAL;")
        result = cursor.fetchone()
        print(f"设置WAL模式: {result[0]}")
        
        # 设置同步模式
        cursor.execute("PRAGMA synchronous=NORMAL;")
        print("设置同步模式: NORMAL")
        
        # 设置缓存大小
        cursor.execute("PRAGMA cache_size=1000;")
        print("设置缓存大小: 1000页")
        
        # 设置临时存储
        cursor.execute("PRAGMA temp_store=MEMORY;")
        print("设置临时存储: 内存")
        
        # 设置忙等待超时
        cursor.execute("PRAGMA busy_timeout=30000;")
        print("设置忙等待超时: 30秒")
        
        # 执行VACUUM优化
        print("执行VACUUM优化...")
        cursor.execute("VACUUM;")
        print("VACUUM完成")
        
        # 分析数据库
        print("分析数据库统计信息...")
        cursor.execute("ANALYZE;")
        print("ANALYZE完成")
        
        # 检查数据库完整性
        print("检查数据库完整性...")
        cursor.execute("PRAGMA integrity_check;")
        integrity_result = cursor.fetchone()
        print(f"完整性检查: {integrity_result[0]}")
        
        # 显示数据库信息
        cursor.execute("PRAGMA database_list;")
        db_info = cursor.fetchall()
        for db in db_info:
            print(f"数据库: {db[1]} -> {db[2]}")
        
        # 显示表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"数据库表数量: {len(tables)}")
        
        # 显示ServerMetrics表的记录数
        try:
            cursor.execute("SELECT COUNT(*) FROM model_storage_servermetrics;")
            count = cursor.fetchone()[0]
            print(f"ServerMetrics记录数: {count}")
        except sqlite3.OperationalError:
            print("ServerMetrics表不存在")
        
        conn.close()
        print("✅ 数据库优化完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库优化失败: {e}")
        return False

def test_database_performance():
    """测试数据库性能"""
    print("\n测试数据库性能...")
    
    try:
        django.setup()
        from apps.model_storage.models import ServerMetrics
        from django.utils import timezone
        import time
        
        # 测试插入性能
        start_time = time.time()
        test_data = {
            'cpu_usage': 10.0,
            'memory_usage': 20.0,
            'disk_usage': 30.0,
            'network_upload': '1MB/s',
            'network_download': '2MB/s',
            'network_total': '3MB/s',
            'timestamp': timezone.now().isoformat()
        }
        
        # 创建测试记录
        metric = ServerMetrics.objects.create(**test_data)
        insert_time = time.time() - start_time
        print(f"插入测试: {insert_time:.3f}秒")
        
        # 测试查询性能
        start_time = time.time()
        count = ServerMetrics.objects.count()
        query_time = time.time() - start_time
        print(f"查询测试: {query_time:.3f}秒 (记录数: {count})")
        
        # 清理测试记录
        metric.delete()
        print("✅ 性能测试完成")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def clean_old_metrics():
    """清理旧的监控数据"""
    print("\n清理旧的监控数据...")
    
    try:
        django.setup()
        from apps.model_storage.models import ServerMetrics
        from django.utils import timezone
        from datetime import timedelta
        
        # 清理7天前的数据
        week_ago = timezone.now() - timedelta(days=7)
        deleted_count = ServerMetrics.objects.filter(timestamp__lt=week_ago).delete()[0]
        print(f"清理了 {deleted_count} 条过期记录")
        
        # 显示当前记录数
        current_count = ServerMetrics.objects.count()
        print(f"当前记录数: {current_count}")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def main():
    """主函数"""
    print("SQLite数据库优化工具")
    print("=" * 50)
    
    # 优化数据库
    if optimize_sqlite_database():
        # 测试性能
        test_database_performance()
        
        # 清理旧数据
        clean_old_metrics()
    
    print("\n" + "=" * 50)
    print("优化完成")
    print("\n建议:")
    print("1. 重启spug服务以应用新的数据库设置")
    print("2. 监控数据库锁定错误是否减少")
    print("3. 定期运行此脚本进行数据库维护")

if __name__ == "__main__":
    main()
