2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:21:38  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:25:25  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:25:25  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:25:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:25:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:25:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:25:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:25:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:25:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 10:25:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 10:25:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 13:39:36  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:23:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:24:07  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:24:08  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:24:08  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:25:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:25:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:25:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 14:56:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 16:54:24  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 16:54:24  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 16:54:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 16:54:27  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:02:46  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:02:46  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:04:47  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:04:47  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:06:13  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:06:13  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:07:08  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:07:08  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:09:53  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:09:53  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:10:16  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:10:16  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:22:55  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:22:55  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:23:21  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:23:21  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:23:39  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:23:39  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:24:03  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:24:03  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:24:28  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:24:28  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:24:57  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:24:57  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:25:19  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:25:19  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:25:44  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:25:44  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:26:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:26:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:27:31  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:27:31  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:28:48  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:28:48  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:33:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:33:26  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:33:50  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:33:50  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:34:18  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:34:18  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:34:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:34:42  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:35:55  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:35:55  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:36:10  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:36:10  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:39:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:39:15  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:48:40  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:48:40  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:49:35  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:49:35  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:50:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:50:14  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 17:50:37  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 17:50:37  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 18:00:41  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 18:00:41  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 18:01:10  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 18:01:10  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 18:01:35  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 18:01:35  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 18:01:58  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 18:01:58  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 18:02:22  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 18:02:22  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 2025-06-30 18:03:03  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:52" -[print]-<module>- [0m
    
    .__   __. .______           __        ______     _______ 
    |  \ |  | |   _  \         |  |      /  __  \   /  _____|
    |   \|  | |  |_)  |  ______|  |     |  |  |  | |  |  __  
    |  . `  | |   _  <  |______|  |     |  |  |  | |  | |_ | 
    |  |\   | |  |_)  |        |  `----.|  `--'  | |  |__| | 
    |__| \__| |______/         |_______| \______/   \______|      
    
         [0m
 2025-06-30 18:03:03  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\nb_log\__init__.py:64" -[print]-<module>- [0m
        1)使用pycharm时候，强烈建议按下面的重新自定义设置pycharm的console里面的主题颜色，否则颜色显示瞎眼，代码里面规定的颜色只是大概的红黄蓝绿。在不同的ide软件和主题、字体下是不同的显示效果，需要用户自己设置。
        设置方式为 打开pycharm的 file -> settings -> Editor -> Color Scheme -> Console Colors 选择monokai，点击展开 ANSI colors，
        并重新修改自定义7个颜色，设置Blue为 0454F3 ，Cyan为 06F0F6 ，Green 为 13FC02 ，Magenta为 ff1cd5 ,red为 F80606 ，yellow为 EAFA04 ，gray 为 FFFFFF ，white 为 FFFFFF 。
        不同版本的pycahrm或主题或ide，可以根据控制台根据实际显示设置。

        2)使用xshell或finashell工具连接linux也可以自定义主题颜色，默认使用shell连接工具的颜色也可以。

        颜色效果如连接 https://ibb.co/qRssXTr

        在当前项目根目录的 nb_log_config.py 中可以修改当get_logger方法不传参时后的默认日志行为。

        nb_log文档 https://nb-log-doc.readthedocs.io/zh_CN/latest/

        为什么要设置pycharm终端颜色，解释为什么\033不能决定最终颜色 https://nb-log-doc.readthedocs.io/zh_CN/latest/articles/c1.html#c
        [0m

        
 