2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:21:38[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000267D0A136D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:21:38[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001ABEA453450>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:21:38[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000021D66493450>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:21:38[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:21:38 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001310E8F3690>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:25:26[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001D8C2AB3750>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:25:26[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000014145553910>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:25:26[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000233998336D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:25:26[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:25:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001449C233590>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 10:25:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 10:25:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 10:25:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m10:25:27[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 10:25:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 10:25:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 10:25:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 10:25:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000250040C3310>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m13:39:37[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m13:39:37[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000017555EC3790>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m13:39:37[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000194F5B532D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m13:39:37[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000017B1DC13490>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m13:39:37[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000015BC55236D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 13:39:37 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000002221CD43950>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:23:15[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000002A2D62D36D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:23:15[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001F97C003690>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:23:15[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000014092133450>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:23:15[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000018690AD37D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:23:15[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:23:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001FD42543590>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:24:07[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000022E752E35D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:24:07[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000222D05A3710>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:24:07[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:24:07 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001ABC3543810>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:24:08[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000019B8F2336D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:24:08[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:24:08 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001D08F5A3390>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:25:14[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001506C4637D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:25:14[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000029868773450>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:25:14[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001CF33883750>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:25:14[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:25:14 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001E80B4637D0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:25:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:25:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:25:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:25:15[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:25:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:25:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:25:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:25:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000026C57B13710>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:56:43[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000002E6378B3690>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:56:43[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001402C813390>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:56:43[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000002D1C7A03790>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:56:43[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000238DC523850>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m14:56:43[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 14:56:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000019AD1863710>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 16:54:24 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 16:54:24 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 16:54:24 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m16:54:24[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 16:54:24 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 16:54:24 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 16:54:24 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 16:54:24 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000258DE993390>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 16:54:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 16:54:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 16:54:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m16:54:27[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 16:54:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 16:54:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 16:54:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 16:54:27 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000018293D63590>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:02:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:02:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:02:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:02:47[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:02:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:02:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:02:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:02:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000160E8CF3490>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:04:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:04:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:04:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:04:47[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:04:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:04:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:04:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:04:47 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000018DED3D3490>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:06:13 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:06:13 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:06:13 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:06:13[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:06:13 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:06:13 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:06:13 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:06:13 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000017A55473650>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:07:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:07:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:07:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:07:09[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:07:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:07:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:07:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:07:09 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000026DF7063710>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:09:53 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:09:53 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:09:53 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:09:53[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:09:53 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:09:53 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:09:53 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:09:53 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000027915393510>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:10:16 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:10:16 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:10:16 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:10:16[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:10:16 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:10:16 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:10:16 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:10:16 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001E554393550>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:22:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:22:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:22:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:22:55[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:22:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:22:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:22:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:22:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000024E3C633710>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:23:21 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:23:21 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:23:21 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:23:21[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:23:21 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:23:21 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:23:21 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:23:21 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001B10F623650>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:23:40 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:23:40 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:23:40 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:23:40[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:23:40 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:23:40 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:23:40 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:23:40 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000211079D3710>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:24:04 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:24:04 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:24:04 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:24:04[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:24:04 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:24:04 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:24:04 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:24:04 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001EF23B23690>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:24:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:24:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:24:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:24:28[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:24:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:24:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:24:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:24:28 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000268C7B83710>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:24:57 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:24:57 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:24:57 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:24:57[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:24:57 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:24:57 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:24:57 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:24:57 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001B83E613D10>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:25:19 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:25:19 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:25:19 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:25:19[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:25:19 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:25:19 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:25:19 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:25:19 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001D1CCA03E90>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:25:44 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:25:44 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:25:44 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:25:44[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:25:44 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:25:44 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:25:44 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:25:44 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000192A3CA3CD0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:26:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:26:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:26:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:26:26[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:26:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:26:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:26:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:26:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000002EA89B53FD0>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:27:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:27:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:27:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:27:31[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:27:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:27:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:27:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:27:31 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000124ED93C090>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:28:48 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:28:48 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:28:48 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:28:48[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:28:48 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:28:48 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:28:48 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:28:48 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001E693E8C110>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:33:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:33:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:33:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:33:26[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:33:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:33:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:33:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:33:26 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000282DB533F50>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:33:50 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:33:50 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:33:50 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:33:50[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:33:50 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:33:50 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:33:50 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:33:50 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000002101E993F90>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:34:18 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:34:18 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:34:18 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:34:18[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:34:18 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:34:18 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:34:18 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:34:18 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001CC1F40C290>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:34:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:34:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:34:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:34:43[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:34:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:34:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:34:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:34:43 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001C031ECC350>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:35:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:35:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:35:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:35:55[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:35:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:35:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:35:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:35:55 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x0000019079BDC290>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:36:10 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:36:10 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:36:10 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:36:10[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:36:10 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:36:10 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:36:10 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:36:10 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x00000255814DC150>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
2025-06-30 17:39:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:64" - show_funboost_flag - DEBUG - [0m

          ___                  ___                    ___                                           ___                    ___                    ___                          
         /  /\                /__/\                  /__/\                  _____                  /  /\                  /  /\                  /  /\                   ___   
        /  /:/_               \  \:\                 \  \:\                /  /::\                /  /::\                /  /::\                /  /:/_                 /  /\  
       /  /:/ /\               \  \:\                 \  \:\              /  /:/\:\              /  /:/\:\              /  /:/\:\              /  /:/ /\               /  /:/  
      /  /:/ /:/           ___  \  \:\            _____\__\:\            /  /:/~/::\            /  /:/  \:\            /  /:/  \:\            /  /:/ /::\             /  /:/   
     /__/:/ /:/           /__/\  \__\:\          /__/::::::::\          /__/:/ /:/\:|          /__/:/ \__\:\          /__/:/ \__\:\          /__/:/ /:/\:\           /  /::\   
     \  \:\/:/            \  \:\ /  /:/          \  \:\~~\~~\/          \  \:\/:/~/:/          \  \:\ /  /:/          \  \:\ /  /:/          \  \:\/:/~/:/          /__/:/\:\  
      \  \::/              \  \:\  /:/            \  \:\  ~~~            \  \::/ /:/            \  \:\  /:/            \  \:\  /:/            \  \::/ /:/           \__\/  \:\ 
       \  \:\               \  \:\/:/              \  \:\                 \  \:\/:/              \  \:\/:/              \  \:\/:/              \__\/ /:/                 \  \:\
        \  \:\               \  \::/                \  \:\                 \  \::/                \  \::/                \  \::/                 /__/:/                   \__\/
         \__\/                \__\/                  \__\/                  \__\/                  \__\/                  \__\/                  \__\/                         



    [0m
2025-06-30 17:39:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:66" - show_funboost_flag - DEBUG - 分布式函数调度框架funboost文档地址：  [0m https://funboost.readthedocs.io/zh-cn/latest/ [0m 
2025-06-30 17:39:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127" - use_config_form_funboost_config_module - DEBUG - [0;93m17:39:15[0m  "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:127"   [0;93;100m
    分布式函数调度框架会自动导入funboost_config模块
    当第一次运行脚本时候，函数调度框架会在你的python当前项目的根目录下 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下，创建一个名为 funboost_config.py 的文件。
    自动读取配置，会优先读取启动脚本的所在目录 C:/Users/<USER>/Documents/GitHub/spug/spug_api 的funboost_config.py文件，
    如果没有 C:/Users/<USER>/Documents/GitHub/spug/spug_api/funboost_config.py 文件，则读取项目根目录 C:/Users/<USER>/scoop/apps/python311/current/python311.zip 下的funboost_config.py做配置。
    只要 funboost_config.py 在任意 PYTHONPATH 的文件夹下，就能自动读取到。
    在 "C:/Users/<USER>/scoop/apps/python311/current/python311.zip/funboost_config.py:1" 文件中，需要按需重新设置要使用到的中间件的键和值，例如没有使用rabbitmq而是使用redis做中间件，则不需要配置rabbitmq。
    [0m

2025-06-30 17:39:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:136" - use_config_form_funboost_config_module - DEBUG - 分布式函数调度框架 读取到
 "C:\Users\<USER>\Documents\GitHub\spug\spug_api\funboost_config.py:1" 文件里面的变量作为优先配置了

2025-06-30 17:39:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:85" - show_frame_config - DEBUG - 显示当前的项目中间件配置参数
2025-06-30 17:39:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:101" - show_frame_config - DEBUG - 读取的 BrokerConnConfig 配置是:
 {
    "MONGO_CONNECT_URL": "mongodb://127.0.0.1:27017",
    "RABBITMQ_USER": "rabbitmq_user",
    "RABBITMQ_PASS": "rab**********",
    "RABBITMQ_HOST": "127.0.0.1",
    "RABBITMQ_PORT": 5672,
    "RABBITMQ_VIRTUAL_HOST": "",
    "RABBITMQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "REDIS_HOST": "127.0.0.1",
    "REDIS_USERNAME": "",
    "REDIS_PASSWORD": "",
    "REDIS_PORT": 6379,
    "REDIS_DB": 7,
    "REDIS_DB_FILTER_AND_RPC_RESULT": 8,
    "REDIS_URL": "redis://:@127.0.0.1:6379/7",
    "NSQD_TCP_ADDRESSES": "['127.0.0.1:4150']",
    "NSQD_HTTP_CLIENT_HOST": "127.0.0.1",
    "NSQD_HTTP_CLIENT_PORT": 4151,
    "KAFKA_BOOTSTRAP_SERVERS": "['127.0.0.1:9092']",
    "KFFKA_SASL_CONFIG": {
        "bootstrap_servers": [
            "127.0.0.1:9092"
        ],
        "sasl_plain_username": "",
        "sasl_plain_password": "",
        "sasl_mechanism": "SCRAM-SHA-256",
        "security_protocol": "SASL_PLAINTEXT"
    },
    "SQLACHEMY_ENGINE_URL": "sqlite:////sqlachemy_queues/queues.db",
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "root",
    "MYSQL_PASSWORD": "123***",
    "MYSQL_DATABASE": "testdb6",
    "SQLLITE_QUEUES_PATH": "/sqllite_queues",
    "TXT_FILE_PATH": "C:\\Users\\<USER>\\Documents\\GitHub\\spug\\spug_api\\txt_queues",
    "ROCKETMQ_NAMESRV_ADDR": "***************:9876",
    "MQTT_HOST": "127.0.0.1",
    "MQTT_TCP_PORT": 1883,
    "HTTPSQS_HOST": "127.0.0.1",
    "HTTPSQS_PORT": "1218",
    "HTTPSQS_AUTH": "123456",
    "NATS_URL": "nats://*************:4222",
    "KOMBU_URL": "redis://127.0.0.1:6379/9",
    "CELERY_BROKER_URL": "redis://127.0.0.1:6379/12",
    "CELERY_RESULT_BACKEND": "redis://127.0.0.1:6379/13",
    "DRAMATIQ_URL": "amqp://rabbitmq_user:rab**********@127.0.0.1:5672/",
    "PULSAR_URL": "pulsar://**************:6650"
} 
2025-06-30 17:39:15 - funboost.prompt - "C:\Users\<USER>\Documents\GitHub\spug\spug_api\venv\Lib\site-packages\funboost\set_frame_config.py:103" - show_frame_config - DEBUG - 读取的 FunboostCommonConfig 配置是:
  {
    "NB_LOG_FORMATER_INDEX_FOR_CONSUMER_AND_PUBLISHER": "<logging.Formatter object at 0x000001EFD331C210>",
    "TIMEZONE": "Asia/Shanghai",
    "SHOW_HOW_FUNBOOST_CONFIG_SETTINGS": true,
    "FUNBOOST_PROMPT_LOG_LEVEL": 10,
    "KEEPALIVETIMETHREAD_LOG_LEVEL": 10
} 
