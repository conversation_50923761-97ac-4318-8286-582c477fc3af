import os
import time
import datetime
import subprocess
import psutil
import json
import logging
from pathlib import Path
from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Q
from django.utils import timezone
from datetime import timedelta
from libs.decorators import auth
from libs.utils import json_response
# 移除rest_framework依赖
from .models import ReleasePlan, ServerMetrics, FileStatus
from .exceptions import (
    ModelStorageException, NetworkException, DatabaseException,
    FileSystemException, TimeoutException, ValidationException,
    log_exception, handle_api_exception, exception_handler,
    network_retry, database_retry, safe_execute
)
from .file_utils import (
    safe_file_ops, network_io_tracker, FileOperationError
)
from .config import (
    network_config, database_config, monitoring_config,
    scan_config, security_config
)

# 配置logger
logger = logging.getLogger('model_storage')

@method_decorator(csrf_exempt, name='dispatch')
class ServerMetricsView(View):
    """服务器指标API"""
    # 添加类级变量用于存储上次的网络IO数据
    _last_net_io = None
    _last_net_time = None
    
    @auth('model.storage.view')
    def get(self, request):
        """获取实时服务器指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 使用free命令获取更准确的内存使用情况
            memory_info = {}
            try:
                free_output = subprocess.check_output(['free', '-m']).decode()
                for line in free_output.split('\n'):
                    if line.startswith('Mem:'):
                        parts = line.split()
                        total = float(parts[1])
                        used = float(parts[2])
                        available = float(parts[6])

                        # 使用更准确的计算方式：(total - available) / total * 100
                        # 或者直接使用 used / total * 100，但available更准确
                        memory_usage_percent = round((total - available) / total * 100, 1)

                        # 记录详细信息用于调试
                        logger.debug(f"内存信息 - 总计: {total}MB, 已用: {used}MB, 可用: {available}MB, 使用率: {memory_usage_percent}%")

                        memory_info['percent'] = memory_usage_percent
                        memory_info['total'] = total
                        memory_info['used'] = used
                        memory_info['available'] = available
                        break
            except Exception as e:
                logger.warning(f"free命令执行失败: {e}")
                # 如果free命令失败，回退到psutil
                memory = psutil.virtual_memory()
                memory_info['percent'] = round(memory.percent, 1)
                memory_info['total'] = round(memory.total / (1024*1024), 1)
                memory_info['used'] = round(memory.used / (1024*1024), 1)
                memory_info['available'] = round(memory.available / (1024*1024), 1)
                logger.debug(f"psutil内存信息 - 总计: {memory_info['total']}MB, 已用: {memory_info['used']}MB, 可用: {memory_info['available']}MB, 使用率: {memory_info['percent']}%")
            disk = psutil.disk_usage('/')

            # 网络速率采集（使用安全的文件操作）
            current_net_io = psutil.net_io_counters()
            current_time = time.time()
            net_speed = {'upload': 0, 'download': 0}

            # 读取上次的网络IO数据
            try:
                last_data = network_io_tracker.load_network_data()
                if last_data:
                    time_delta = current_time - last_data['time']
                    if time_delta > 0:
                        upload_speed = (current_net_io.bytes_sent - last_data['bytes_sent']) / time_delta / (1024 * 1024)
                        download_speed = (current_net_io.bytes_recv - last_data['bytes_recv']) / time_delta / (1024 * 1024)
                        net_speed['upload'] = round(max(0, upload_speed), 2)  # 确保非负值
                        net_speed['download'] = round(max(0, download_speed), 2)
            except FileOperationError as e:
                logger.warning(f"读取网络IO历史数据失败: {e}")
            except Exception as e:
                log_exception(FileSystemException(f"网络IO数据处理异常: {str(e)}", operation="read"))

            # 保存当前网络IO数据
            try:
                network_io_tracker.save_network_data(
                    current_net_io.bytes_sent,
                    current_net_io.bytes_recv,
                    current_time
                )
            except Exception as e:
                log_exception(FileSystemException(f"保存网络IO数据异常: {str(e)}", operation="write"))

            # 定期清理旧数据（每100次调用清理一次）
            if hasattr(self, '_cleanup_counter'):
                self._cleanup_counter += 1
            else:
                self._cleanup_counter = 1

            if self._cleanup_counter >= 100:
                self._cleanup_counter = 0
                try:
                    network_io_tracker.cleanup_old_data()
                except Exception as e:
                    logger.warning(f"清理旧网络IO数据失败: {e}")

            metrics = {
                'cpu_usage': round(cpu_percent, 1),
                'memory_usage': memory_info['percent'],
                'disk_usage': round(disk.percent, 1),
                'network_upload': f"{net_speed['upload']:.2f}MB/s",
                'network_download': f"{net_speed['download']:.2f}MB/s",
                'network_total': f"{(net_speed['upload'] + net_speed['download']):.2f}MB/s",
                'timestamp': timezone.now().isoformat()
            }
            
            # 保存到数据库
            def save_metrics():
                ServerMetrics.objects.create(**metrics)
                # 清理7天前的数据
                week_ago = timezone.now() - timedelta(days=7)
                ServerMetrics.objects.filter(timestamp__lt=week_ago).delete()

            try:
                database_retry.execute_with_retry(save_metrics)
            except DatabaseException as e:
                logger.error(f"保存服务器指标到数据库失败: {e.message}", extra=e.details)
            except Exception as e:
                log_exception(DatabaseException(f"数据库操作异常: {str(e)}", "save_metrics"))
            
            return JsonResponse({'data': metrics})
            
        except Exception as e:
            return JsonResponse({
                'data': {
                    'cpu_usage': 0,
                    'memory_usage': 0, 
                    'disk_usage': 0,
                    'network_upload': '0MB/s',
                    'network_download': '0MB/s',
                    'network_total': '0MB/s',
                    'timestamp': timezone.now().isoformat(),
                    'error': f'获取系统指标失败: {str(e)}'
                }
            })

@method_decorator(csrf_exempt, name='dispatch')
class FileCompareView(View):
    @auth('model.storage.view')
    def get(self, request):
        """获取文件对比数据（Git风格）"""
        try:
            # 模拟SVN文件状态检查
            file_compare_data = self.check_file_differences()
            return JsonResponse({'data': file_compare_data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})
    
    def check_file_differences(self):
        """检查本地与远程文件差异"""
        # 使用真实数据：本地tree扫描 + 远程HTTP获取
        base_path = "/HDD_Raid/SVN_MODEL_REPO"
        
        try:
            # 获取本地文件树
            local_files = self.scan_local_tree(base_path)
            # 获取远程文件列表
            remote_files = self.scan_remote_files()
            # 对比差异
            return self.compare_files(local_files, remote_files)
        except Exception as e:
            print(f"获取真实数据失败，使用模拟数据: {e}")
            return self.get_mock_data(base_path)
    
    def scan_local_tree(self, base_path, max_depth=3):
        """使用tree命令扫描本地目录（限制深度避免过慢）"""
        import subprocess
        import os

        # 安全检查：验证路径是否被允许
        if not security_config.is_path_allowed(base_path):
            logger.warning(f"路径不被允许访问: {base_path}")
            raise FileSystemException(f"路径不被允许访问: {base_path}", base_path, "scan")

        if not os.path.exists(base_path):
            logger.warning(f"本地路径不存在: {base_path}")
            return []
        
        try:
            # 使用tree命令，限制深度和文件类型
            cmd = [
                'tree', base_path,
                '-L', str(max_depth),  # 限制深度
                '-I', '*.log|*.tmp|*.cache|*~',  # 忽略临时文件
                '-F',  # 显示文件类型标识
                '--dirsfirst',  # 目录优先
                '-a',  # 显示隐藏文件
                '--charset=utf-8'  # 使用UTF-8编码
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return self.parse_tree_output(result.stdout, base_path)
            else:
                print(f"tree命令执行失败: {result.stderr}")
                return self.scan_local_python(base_path)
                
        except subprocess.TimeoutExpired:
            print("tree命令超时，切换到Python扫描")
            return self.scan_local_python(base_path)
        except Exception as e:
            print(f"tree命令出错: {e}")
            return self.scan_local_python(base_path)
    
    def scan_local_python(self, base_path, max_files=1000):
        """Python方式扫描本地目录（作为fallback）"""
        import os
        files = []
        file_count = 0
        
        try:
            for root, dirs, filenames in os.walk(base_path):
                if file_count >= max_files:
                    break
                    
                # 跳过隐藏目录和临时目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and 'cache' not in d.lower()]
                
                # 限制深度
                depth = root.replace(base_path, '').count(os.sep)
                if depth > 3:
                    continue
                
                # 添加目录
                rel_path = os.path.relpath(root, base_path)
                if rel_path != '.':
                    files.append({
                        'path': root,
                        'name': os.path.basename(root),
                        'type': 'folder',
                        'size': self.get_dir_size(root),
                        'lastModified': self.get_file_time(root)
                    })
                
                # 添加重要文件
                for filename in filenames:
                    if file_count >= max_files:
                        break
                    
                    # 只关注重要文件类型
                    if self.is_important_file(filename):
                        file_path = os.path.join(root, filename)
                        files.append({
                            'path': file_path,
                            'name': filename,
                            'type': 'file',
                            'size': self.format_file_size(os.path.getsize(file_path)),
                            'lastModified': self.get_file_time(file_path)
                        })
                        file_count += 1
                        
        except Exception as e:
            print(f"Python扫描出错: {e}")
            
        return files
    
    def parse_tree_output(self, tree_output, base_path):
        """解析tree命令输出"""
        files = []
        lines = tree_output.split('\n')
        
        for line in lines:
            if not line.strip() or '├──' not in line and '└──' not in line:
                continue
                
            # 提取文件名和类型
            clean_line = line.replace('├──', '').replace('└──', '').strip()
            if not clean_line:
                continue
                
            # 判断是文件还是目录
            is_dir = clean_line.endswith('/') or not '.' in clean_line
            name = clean_line.rstrip('/')
            
            # 构造完整路径（简化处理）
            full_path = f"{base_path}/{name}"
            
            files.append({
                'path': full_path,
                'name': name,
                'type': 'folder' if is_dir else 'file',
                'size': 'DIR' if is_dir else 'Unknown',
                'lastModified': 'Unknown'
            })
            
        return files
    
    def scan_remote_files(self):
        """扫描远程仓库文件列表"""
        import requests
        from urllib.parse import urljoin
        
        remote_url = network_config.get_remote_url()
        # 使用配置的认证信息
        auth = network_config.get_auth_credentials()
        
        def fetch_remote_files():
            import requests
            # 获取Model目录列表
            model_url = urljoin(remote_url, "Model/")
            timeout = network_config.get_timeout()
            response = requests.get(model_url, timeout=timeout, auth=auth)
            response.raise_for_status()
            return response

        try:
            response = network_retry.execute_with_retry(fetch_remote_files)

            # 解析HTML获取模型列表
            remote_models = self.parse_remote_html(response.text, urljoin(remote_url, "Model/"))

            # 获取每个模型的详细信息
            detailed_files = []
            for model in remote_models:
                if model['type'] == 'folder':
                    model_details = safe_execute(
                        self.get_model_details,
                        remote_url, model['name'], auth,
                        default_return=[],
                        context={'model_name': model['name'], 'operation': 'get_model_details'}
                    )
                    if model_details:
                        detailed_files.extend(model_details)

            return detailed_files

        except (NetworkException, TimeoutException) as e:
            logger.error(f"获取远程文件列表失败: {e.message}", extra=e.details)
            return []
        except Exception as e:
            log_exception(NetworkException(f"远程文件扫描异常: {str(e)}", remote_url))
            return []
    
    def parse_remote_html(self, html_content, base_url):
        """解析远程目录的HTML内容"""
        import re
        files = []
        
        # 简单的正则表达式匹配文件链接
        link_pattern = r'<a href="([^"]+)"[^>]*>([^<]+)</a>'
        matches = re.findall(link_pattern, html_content)
        
        for href, text in matches:
            if href.startswith('..') or href.startswith('/'):
                continue
                
            is_dir = href.endswith('/')
            name = text.strip()
            if is_dir:
                name = name.rstrip('/')
            
            files.append({
                'path': f"{base_url}{href}",
                'name': name,
                'type': 'folder' if is_dir else 'file',
                'size': 'DIR' if is_dir else 'Unknown',
                'lastModified': 'Unknown',
                'source': 'remote'
            })
            
        return files
    
    def get_model_details(self, base_url, model_name, auth):
        """获取单个模型的详细文件信息"""
        import requests
        from urllib.parse import urljoin
        
        model_files = []
        try:
            model_url = urljoin(base_url, f"Model/{model_name}/")
            response = requests.get(model_url, timeout=10, auth=auth)
            response.raise_for_status()
            
            # 解析模型目录内容
            files_in_model = self.parse_remote_html(response.text, model_url)
            
            # 判断模型目录状态
            if len(files_in_model) == 0:
                # 空目录
                status = 'missing'
                diff_summary = '远程模型目录为空'
            elif any(f['name'] == 'md5sums.txt' for f in files_in_model):
                # 包含校验文件，认为是完整的
                status = 'synced'
                diff_summary = '远程模型文件完整'
            else:
                # 没有校验文件，可能不完整
                status = 'partial'
                diff_summary = '远程模型文件可能不完整（缺少md5sums.txt）'
            
            # 添加模型目录信息
            model_files.append({
                'path': model_url,
                'name': model_name,
                'type': 'folder',
                'size': f"{len(files_in_model)} items",
                'lastModified': 'Unknown',
                'status': status,
                'diff_summary': diff_summary,
                'source': 'remote',
                'file_count': len(files_in_model)
            })
            
        except Exception as e:
            print(f"获取模型 {model_name} 详情失败: {e}")
            # 添加错误信息
            model_files.append({
                'path': f"{base_url}Model/{model_name}/",
                'name': model_name,
                'type': 'folder',
                'size': 'Error',
                'lastModified': 'Unknown',
                'status': 'missing',
                'diff_summary': f'无法访问远程模型目录: {str(e)}',
                'source': 'remote',
                'file_count': 0
            })
            
        return model_files
    
    def compare_files(self, local_files, remote_files):
        """对比本地和远程文件差异"""
        # 创建文件路径映射
        local_map = {f['name']: f for f in local_files}
        remote_map = {f['name']: f for f in remote_files}
        
        compared_files = []
        
        # 检查本地文件状态
        for name, local_file in local_map.items():
            if name in remote_map:
                # 文件在两边都存在，检查是否有差异
                status = 'synced'  # 简化处理，实际需要比较修改时间或hash
                diff_summary = '文件已同步'
            else:
                # 本地有，远程没有
                status = 'added'
                diff_summary = '本地新增，远程尚未同步'
            
            compared_files.append({
                **local_file,
                'status': status,
                'diff_summary': diff_summary
            })
        
        # 检查远程独有的文件
        for name, remote_file in remote_map.items():
            if name not in local_map:
                compared_files.append({
                    **remote_file,
                    'status': 'missing',
                    'diff_summary': '远程有此文件，本地缺失'
                })
        
        return compared_files
    
    def is_important_file(self, filename):
        """判断是否为重要文件"""
        important_extensions = [
            '.tar.gz', '.zip', '.tar', '.7z',  # 压缩文件
            '.bin', '.safetensors', '.pt', '.pth',  # 模型文件
            '.txt', '.md', '.json', '.yaml', '.yml',  # 配置文件
            '.sh', '.bat', '.py', '.dockerfile'  # 脚本文件
        ]
        
        filename_lower = filename.lower()
        return any(filename_lower.endswith(ext) for ext in important_extensions) or \
               filename_lower in ['readme', 'license', 'changelog', 'md5sums.txt']
    
    def get_dir_size(self, path):
        """获取目录大小（简化实现）"""
        try:
            import os
            total = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    try:
                        fp = os.path.join(dirpath, filename)
                        total += os.path.getsize(fp)
                    except:
                        continue
            return self.format_file_size(total)
        except:
            return 'Unknown'
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"
    
    def get_file_time(self, path):
        """获取文件修改时间"""
        try:
            import os
            import datetime
            timestamp = os.path.getmtime(path)
            return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return 'Unknown'
    
    def get_mock_data(self, base_path):
        """获取模拟数据（fallback）"""
        
        files = [
            {
                'path': f"{base_path}/Model/Baichuan2-13B/model.bin",
                'filename': 'model.bin',
                'status': 'synced',
                'type': 'file',
                'size': '2.5GB',
                'lastModified': '2024-01-15 10:30:00',
                'diff_summary': '文件已同步'
            },
            {
                'path': f"{base_path}/Model/Baichuan2-13B/md5sums.txt",
                'filename': 'md5sums.txt',
                'status': 'modified',
                'type': 'file',
                'size': '1.2KB',
                'lastModified': '2024-01-16 14:25:00',
                'diff_summary': '本地文件已修改，需要同步'
            },
            {
                'path': f"{base_path}/Model/DeepSeek-V3-0324-P800/tokenizer.tar.gz",
                'filename': 'tokenizer.tar.gz',
                'status': 'missing',
                'type': 'file',
                'size': '150MB',
                'lastModified': '2024-01-14 09:15:00',
                'diff_summary': '远程有此文件，本地缺失'
            },
            {
                'path': f"{base_path}/Model/Qwen2.5-7B",
                'filename': 'Qwen2.5-7B',
                'status': 'added',
                'type': 'folder',
                'size': '7.2GB',
                'lastModified': '2024-01-17 11:00:00',
                'diff_summary': '本地新增文件夹，远程尚未同步'
            },
            {
                'path': f"{base_path}/Vendor/Kunlunxin/P800/Bert-base/Inference/v1.0/doc",
                'filename': 'doc',
                'status': 'conflict',
                'type': 'folder',
                'size': '25MB',
                'lastModified': '2024-01-16 16:45:00',
                'diff_summary': '本地和远程都有修改，存在冲突'
            },
            {
                'path': f"{base_path}/Vendor/AMD/RX7900/YOLOv5/Training/v2.1",
                'filename': 'v2.1',
                'status': 'deleted',
                'type': 'folder',
                'size': '1.8GB',
                'lastModified': '2024-01-10 08:20:00',
                'diff_summary': '远程已删除，本地仍存在'
            },
            {
                'path': f"{base_path}/Model/Qwen3-4B/config.json",
                'filename': 'config.json',
                'status': 'synced',
                'type': 'file',
                'size': '2.1KB',
                'lastModified': '2024-01-15 12:00:00',
                'diff_summary': '文件已同步'
            },
            {
                'path': f"{base_path}/Vendor/Biren/BR100/ResNet50/Inference/v1.5/modelzoo.tar.gz",
                'filename': 'modelzoo.tar.gz',
                'status': 'modified',
                'type': 'file',
                'size': '890MB',
                'lastModified': '2024-01-16 13:30:00',
                'diff_summary': '文件已更新，建议同步'
            }
        ]
        
        return files

@method_decorator(csrf_exempt, name='dispatch')
class CheckDifferencesView(View):
    @auth('model.storage.view')
    def get(self, request):
        """检查文件差异"""
        try:
            # 这里可以调用SVN或其他版本控制命令来检查差异
            differences = self.scan_differences()
            
            return JsonResponse({
                'data': {
                    'total_differences': differences['total'],
                    'modified': differences['modified'],
                    'missing': differences['missing'],
                    'conflicts': differences['conflicts'],
                    'scan_time': timezone.now().isoformat()
                }
            })
        except Exception as e:
            return JsonResponse({'data': {'error': str(e)}})
    
    def scan_differences(self):
        """扫描文件差异"""
        # 模拟差异扫描结果
        return {
            'total': 15,
            'modified': 8,
            'missing': 4,
            'conflicts': 2,
            'added': 1
        }

@method_decorator(csrf_exempt, name='dispatch')
class SyncSingleFileView(View):
    @auth('model.storage.edit')
    def post(self, request):
        """同步单个文件"""
        try:
            data = json.loads(request.body)
            file_path = data.get('file_path')
            
            if not file_path:
                return JsonResponse({'error': '文件路径不能为空'}, status=400)
            
            # 模拟文件同步过程
            result = self.sync_file(file_path)
            
            return JsonResponse({'data': result})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def sync_file(self, file_path):
        """执行文件同步"""
        # 这里应该调用实际的SVN或rsync命令
        # 模拟同步过程
        import time
        time.sleep(1)  # 模拟同步时间
        
        return {
            'file_path': file_path,
            'status': 'success',
            'message': '文件同步成功',
            'sync_time': timezone.now().isoformat()
        }

@method_decorator(csrf_exempt, name='dispatch') 
class BatchSyncView(View):
    @auth('model.storage.edit')
    def post(self, request):
        """批量同步文件"""
        try:
            data = json.loads(request.body)
            files = data.get('files', [])
            
            if not files:
                return JsonResponse({'error': '没有要同步的文件'}, status=400)
            
            results = []
            for file_path in files:
                try:
                    result = self.sync_file(file_path)
                    results.append(result)
                except Exception as e:
                    results.append({
                        'file_path': file_path,
                        'status': 'error',
                        'message': str(e)
                    })
            
            return JsonResponse({
                'data': {
                    'total_files': len(files),
                    'successful': len([r for r in results if r['status'] == 'success']),
                    'failed': len([r for r in results if r['status'] == 'error']),
                    'results': results,
                    'sync_time': timezone.now().isoformat()
                }
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def sync_file(self, file_path):
        """执行单个文件同步"""
        # 模拟同步过程
        import time
        import random
        time.sleep(random.uniform(0.5, 2))  # 模拟同步时间
        
        # 90%的成功率
        if random.random() < 0.9:
            return {
                'file_path': file_path,
                'status': 'success',
                'message': '同步成功'
            }
        else:
            return {
                'file_path': file_path, 
                'status': 'error',
                'message': '同步失败：网络连接超时'
            }

@method_decorator(csrf_exempt, name='dispatch')
class FileTreeView(View):
    """文件树API"""
    
    @auth('model.storage.view')
    def get(self, request):
        """获取文件树结构（保持兼容性）"""
        try:
            # 为了兼容性，保留原有的文件树API
            tree_data = [
                {
                    'name': 'Model',
                    'type': 'folder',
                    'status': 'partial',
                    'children': [
                        {
                            'name': 'Baichuan2-13B',
                            'type': 'folder', 
                            'status': 'synced',
                            'children': [
                                {'name': 'model.bin', 'type': 'file', 'status': 'synced', 'size': '2.5GB'},
                                {'name': 'md5sums.txt', 'type': 'file', 'status': 'modified', 'size': '1.2KB'}
                            ]
                        },
                        {
                            'name': 'DeepSeek-V3-0324-P800',
                            'type': 'folder',
                            'status': 'missing',
                            'children': []
                        }
                    ]
                },
                {
                    'name': 'Vendor',
                    'type': 'folder',
                    'status': 'partial',
                    'children': [
                        {
                            'name': 'Kunlunxin',
                            'type': 'folder',
                            'status': 'synced',
                            'children': [
                                {
                                    'name': 'P800',
                                    'type': 'folder',
                                    'status': 'synced',
                                    'children': [
                                        {'name': 'Bert-base', 'type': 'folder', 'status': 'synced'},
                                        {'name': 'ResNet50', 'type': 'folder', 'status': 'synced'},
                                        {'name': 'Yolov5', 'type': 'folder', 'status': 'missing'}
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
            
            return JsonResponse({'data': tree_data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})

@method_decorator(csrf_exempt, name='dispatch')
class LazyLoadTreeView(View):
    """分层懒加载树结构"""
    
    @auth('model.storage.view')
    def get(self, request):
        """获取指定路径的子节点"""
        path = request.GET.get('path', '/HDD_Raid/SVN_MODEL_REPO')
        depth = int(request.GET.get('depth', 1))
        
        try:
            # 获取子节点数据
            children = self.get_children(path, depth)
            
            return JsonResponse({
                'data': {
                    'path': path,
                    'children': children,
                    'total': len(children)
                }
            })
            
        except Exception as e:
            print(f"懒加载出错: {e}")
            return JsonResponse({
                'data': {
                    'path': path,
                    'children': [],
                    'total': 0,
                    'error': str(e)
                }
            })
    
    def get_children(self, parent_path, depth=1):
        """获取指定目录的直接子节点"""
        import os
        
        if not os.path.exists(parent_path):
            return []
        
        children = []
        
        try:
            # 只扫描一层子目录
            for item in os.listdir(parent_path):
                item_path = os.path.join(parent_path, item)
                
                if os.path.isdir(item_path):
                    # 检查是否有子目录（用于显示展开按钮）
                    has_children = self.has_subdirectories(item_path)
                    
                    children.append({
                        'key': item_path,
                        'title': item,
                        'type': 'folder',
                        'isLeaf': not has_children,
                        'size': self.get_quick_dir_size(item_path),
                        'lastModified': self.get_file_time(item_path),
                        'status': 'synced',  # 简化处理
                        'children': [] if not has_children else None  # None表示未加载
                    })
                elif self.is_important_file(item):
                    # 只添加重要文件
                    children.append({
                        'key': item_path,
                        'title': item,
                        'type': 'file',
                        'isLeaf': True,
                        'size': self.format_file_size(os.path.getsize(item_path)),
                        'lastModified': self.get_file_time(item_path),
                        'status': 'synced'
                    })
                    
            # 按类型和名称排序：目录在前，文件在后
            children.sort(key=lambda x: (x['type'] == 'file', x['title'].lower()))
            
        except Exception as e:
            print(f"扫描目录失败: {e}")
            
        return children
    
    def has_subdirectories(self, path):
        """快速检查目录是否有子目录"""
        try:
            for item in os.listdir(path):
                if os.path.isdir(os.path.join(path, item)):
                    return True
        except:
            pass
        return False
    
    def get_quick_dir_size(self, path):
        """快速获取目录大小（只计算直接文件）"""
        try:
            import os
            total = 0
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isfile(item_path):
                    try:
                        total += os.path.getsize(item_path)
                    except:
                        continue
            return self.format_file_size(total) if total > 0 else 'DIR'
        except:
            return 'DIR'
    
    def is_important_file(self, filename):
        """判断是否为重要文件"""
        important_extensions = [
            '.tar.gz', '.zip', '.tar', '.7z',  # 压缩文件
            '.bin', '.safetensors', '.pt', '.pth',  # 模型文件
            '.txt', '.md', '.json', '.yaml', '.yml',  # 配置文件
            '.sh', '.bat', '.py', '.dockerfile'  # 脚本文件
        ]
        
        filename_lower = filename.lower()
        return any(filename_lower.endswith(ext) for ext in important_extensions) or \
               filename_lower in ['readme', 'license', 'changelog', 'md5sums.txt']
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"
    
    def get_file_time(self, path):
        """获取文件修改时间"""
        try:
            import os
            import datetime
            timestamp = os.path.getmtime(path)
            return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return 'Unknown'

@method_decorator(csrf_exempt, name='dispatch')
class AsyncScanView(View):
    """异步扫描文件树"""
    
    @auth('model.storage.view')
    def post(self, request):
        """启动异步扫描任务"""
        try:
            data = json.loads(request.body)
            base_path = data.get('path', '/HDD_Raid/SVN_MODEL_REPO')
            scan_type = data.get('type', 'local')  # local, remote, diff
            
            # 这里可以集成异步任务队列（如Celery）
            # 现在简化为同步处理，但限制扫描范围
            
            if scan_type == 'local':
                result = self.scan_local_limited(base_path)
            elif scan_type == 'remote':
                result = self.scan_remote_limited()
            else:
                result = {'status': 'error', 'message': '不支持的扫描类型'}
            
            return JsonResponse({'data': result})
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def scan_local_limited(self, base_path, max_items=500):
        """限制数量的本地扫描"""
        import os
        import time
        
        start_time = time.time()
        items = []
        count = 0
        
        try:
            for root, dirs, files in os.walk(base_path):
                if count >= max_items or (time.time() - start_time) > 30:  # 30秒超时
                    break
                    
                # 限制深度
                depth = root.replace(base_path, '').count(os.sep)
                if depth > 2:
                    dirs.clear()  # 停止deeper搜索
                    continue
                
                # 添加重要文件
                for filename in files:
                    if count >= max_items:
                        break
                    if self.is_important_file(filename):
                        items.append({
                            'path': os.path.join(root, filename),
                            'name': filename,
                            'type': 'file'
                        })
                        count += 1
                        
        except Exception as e:
            print(f"异步扫描出错: {e}")
            
        return {
            'status': 'completed',
            'items': items,
            'count': count,
            'duration': round(time.time() - start_time, 2)
        }
    
    def scan_remote_limited(self):
        """限制数量的远程扫描"""
        import requests
        import time
        
        start_time = time.time()
        remote_url = "http://***********/GPU_MODEL_REPO/01.DEV/"
        
        try:
            response = requests.get(remote_url, timeout=15)
            if response.status_code == 200:
                items = self.parse_remote_html(response.text, remote_url)
                return {
                    'status': 'completed',
                    'items': items[:100],  # 限制返回数量
                    'count': len(items),
                    'duration': round(time.time() - start_time, 2)
                }
        except Exception as e:
            print(f"远程扫描失败: {e}")
            
        return {
            'status': 'error',
            'message': '远程扫描失败',
            'duration': round(time.time() - start_time, 2)
        }
    
    def parse_remote_html(self, html_content, base_url):
        """解析远程目录的HTML内容"""
        import re
        files = []
        
        # 简单的正则表达式匹配文件链接
        link_pattern = r'<a href="([^"]+)"[^>]*>([^<]+)</a>'
        matches = re.findall(link_pattern, html_content)
        
        for href, text in matches:
            if href.startswith('..') or href.startswith('/'):
                continue
                
            is_dir = href.endswith('/')
            name = text.strip()
            if is_dir:
                name = name.rstrip('/')
            
            files.append({
                'path': f"{base_url}{href}",
                'name': name,
                'type': 'folder' if is_dir else 'file',
                'size': 'DIR' if is_dir else 'Unknown',
                'lastModified': 'Unknown',
                'source': 'remote'
            })
            
        return files
    
    def is_important_file(self, filename):
        """判断是否为重要文件"""
        important_extensions = [
            '.tar.gz', '.zip', '.tar', '.7z',  # 压缩文件
            '.bin', '.safetensors', '.pt', '.pth',  # 模型文件
            '.txt', '.md', '.json', '.yaml', '.yml',  # 配置文件
            '.sh', '.bat', '.py', '.dockerfile'  # 脚本文件
        ]
        
        filename_lower = filename.lower()
        return any(filename_lower.endswith(ext) for ext in important_extensions) or \
               filename_lower in ['readme', 'license', 'changelog', 'md5sums.txt']

@method_decorator(csrf_exempt, name='dispatch')
class ReleasePlanView(View):
    """发布计划API"""
    
    @auth('model.storage.view')
    def get(self, request):
        """获取发布计划列表"""
        try:
            plans = ReleasePlan.objects.all().order_by('-release_date')
            data = [{
                'id': plan.id,
                'card_model': plan.card_model,
                'model_name': plan.model_name,
                'release_date': plan.release_date.strftime('%Y-%m-%d'),
                'model_status': plan.model_status,
                'vendor_status': plan.vendor_status,
                'overall_status': plan.overall_status,
                'created_at': plan.created_at.isoformat() if plan.created_at else None
            } for plan in plans]
            
            return JsonResponse({'data': data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})
    
    @auth('model.storage.edit')
    def post(self, request):
        """创建发布计划"""
        try:
            data = json.loads(request.body)
            plan = ReleasePlan.objects.create(
                card_model=data['card_model'],
                model_name=data['model_name'],
                release_date=data['release_date'],
                model_status=data.get('model_status', 'inProgress'),
                vendor_status=data.get('vendor_status', 'inProgress'),
                overall_status=data.get('overall_status', 'preparing')
            )
            
            return JsonResponse({
                'data': {
                    'id': plan.id,
                    'message': '发布计划创建成功'
                }
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    @auth('model.storage.edit')
    def put(self, request):
        """更新发布计划"""
        try:
            data = json.loads(request.body)
            plan = ReleasePlan.objects.get(id=data['id'])
            
            plan.card_model = data['card_model']
            plan.model_name = data['model_name']
            plan.release_date = data['release_date']
            plan.model_status = data.get('model_status', plan.model_status)
            plan.vendor_status = data.get('vendor_status', plan.vendor_status)
            plan.overall_status = data.get('overall_status', plan.overall_status)
            plan.save()
            
            return JsonResponse({
                'data': {
                    'id': plan.id,
                    'message': '发布计划更新成功'
                }
            })
        except ReleasePlan.DoesNotExist:
            return JsonResponse({'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    @auth('model.storage.del')
    def delete(self, request):
        """删除发布计划"""
        try:
            data = json.loads(request.body)
            plan = ReleasePlan.objects.get(id=data['id'])
            plan.delete()
            
            return JsonResponse({
                'data': {
                    'message': '发布计划删除成功'
                }
            })
        except ReleasePlan.DoesNotExist:
            return JsonResponse({'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class SvnCompareView(View):
    """SVN远程对比"""
    
    @auth('model.storage.view')
    def get(self, request):
        """SVN远程对比"""
        try:
            compare_data = [
                {
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Model/Baichuan2-13B/model.bin',
                    'local_status': 'synced',
                    'remote_status': 'synced',
                    'diff': '无差异'
                },
                {
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Model/DeepSeek-V3/tokenizer.json',
                    'local_status': 'missing',
                    'remote_status': 'synced',
                    'diff': '本地缺失'
                },
                {
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor/Kunlunxin/P800/config.xml',
                    'local_status': 'modified',
                    'remote_status': 'synced',
                    'diff': '本地已修改'
                }
            ]
            
            return JsonResponse({'data': compare_data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})

@method_decorator(csrf_exempt, name='dispatch')
class CheckMissingView(View):
    """检测缺失文件（保持兼容性）"""
    
    @auth('model.storage.view')
    def get(self, request):
        """检测缺失文件（保持兼容性）"""
        try:
            missing_data = {
                'missing_count': 5,
                'missing_files': [
                    '/Model/DeepSeek-V3/config.json',
                    '/Vendor/AMD/RX7900/benchmark.bin',
                    '/Vendor/Biren/BR100/driver.so'
                ],
                'check_time': timezone.now().isoformat()
            }
            
            return JsonResponse({'data': missing_data})
        except Exception as e:
            return JsonResponse({'data': {'error': str(e)}})

@method_decorator(csrf_exempt, name='dispatch')
class RemoteTreeDetailView(View):
    """远程目录详细信息"""
    
    @auth('model.storage.view')
    def get(self, request):
        """获取远程目录的详细文件列表"""
        path = request.GET.get('path', '')
        
        try:
            # 解析远程路径，获取具体的目录信息
            remote_files = self.get_remote_directory_details(path)
            return JsonResponse({'data': remote_files})
        except Exception as e:
            print(f"获取远程目录详情失败: {e}")
            return JsonResponse({'data': [], 'error': str(e)})
    
    def get_remote_directory_details(self, path):
        """获取远程目录的详细信息"""
        import requests
        from urllib.parse import urljoin
        
        # SVN远程仓库配置
        base_url = "http://***********/GPU_MODEL_REPO/01.DEV/"
        auth = ('sys49169', 'Aa123,.,.') 
        
        try:
            # 根据路径类型构建远程URL
            if '/Model/' in path:
                # 模型路径处理
                model_name = path.split('/Model/')[-1].strip('/')
                remote_url = urljoin(base_url, f"Model/{model_name}/")
            elif '/Vendor/' in path:
                # 厂商路径处理  
                vendor_path = path.split('/Vendor/')[-1].strip('/')
                remote_url = urljoin(base_url, f"Vendor/{vendor_path}/")
            else:
                # 根目录处理
                remote_url = base_url
            
            print(f"正在访问远程URL: {remote_url}")
            
            response = requests.get(remote_url, timeout=15, auth=auth)
            response.raise_for_status()
            
            # 解析HTML内容
            files = self.parse_directory_html(response.text, remote_url)
            
            return files
            
        except Exception as e:
            print(f"获取远程目录失败: {e}")
            return []
    
    def parse_directory_html(self, html_content, base_url):
        """解析目录HTML，提取文件信息"""
        import re
        from datetime import datetime
        
        files = []
        
        # 更精确的正则表达式，提取文件信息
        # 匹配格式：<a href="filename">filename</a> 以及可能的日期和大小信息
        link_pattern = r'<a href="([^"]+)"[^>]*>([^<]+)</a>\s*([^<]*)'
        matches = re.findall(link_pattern, html_content)
        
        for href, name, extra_info in matches:
            # 跳过父目录链接
            if href.startswith('..') or href.startswith('/'):
                continue
            
            # 清理文件名
            clean_name = name.strip()
            is_directory = href.endswith('/')
            
            if is_directory:
                clean_name = clean_name.rstrip('/')
            
            # 解析额外信息（日期、大小等）
            file_size = 'DIR' if is_directory else self.extract_size_from_info(extra_info)
            last_modified = self.extract_date_from_info(extra_info)
            
            # 判断文件状态
            status = self.determine_file_status(clean_name, is_directory)
            
            files.append({
                'name': clean_name,
                'path': f"{base_url}{href}",
                'type': 'folder' if is_directory else 'file',
                'size': file_size,
                'lastModified': last_modified,
                'status': status,
                'source': 'remote'
            })
        
        return files
    
    def extract_size_from_info(self, info_text):
        """从额外信息中提取文件大小"""
        import re
        
        # 匹配文件大小格式: 123K, 456M, 789G等
        size_pattern = r'(\d+(?:\.\d+)?)\s*([KMGT]?B?)'
        match = re.search(size_pattern, info_text, re.IGNORECASE)
        
        if match:
            size, unit = match.groups()
            return f"{size}{unit.upper()}"
        
        # 如果没有匹配到大小，检查是否有字节数
        byte_pattern = r'(\d+)\s*bytes?'
        byte_match = re.search(byte_pattern, info_text, re.IGNORECASE)
        
        if byte_match:
            bytes_size = int(byte_match.group(1))
            return self.format_file_size(bytes_size)
        
        return 'Unknown'
    
    def extract_date_from_info(self, info_text):
        """从额外信息中提取修改日期"""
        import re
        from datetime import datetime
        
        # 匹配日期格式: DD-Mon-YYYY HH:MM
        date_pattern = r'(\d{1,2}-\w{3}-\d{4}\s+\d{1,2}:\d{2})'
        match = re.search(date_pattern, info_text)
        
        if match:
            return match.group(1)
        
        # 匹配其他可能的日期格式
        alt_date_pattern = r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})'
        alt_match = re.search(alt_date_pattern, info_text)
        
        if alt_match:
            return alt_match.group(1)
        
        return 'Unknown'
    
    def determine_file_status(self, filename, is_directory):
        """根据文件名和类型判断状态"""
        # 重要文件检查
        important_files = ['md5sums.txt', 'readme', 'license', 'config.json']
        
        if is_directory:
            # 目录状态逻辑
            if any(important in filename.lower() for important in ['model', 'vendor']):
                return 'synced'
            return 'synced'
        else:
            # 文件状态逻辑
            if filename.lower() in important_files:
                return 'synced'
            elif filename.lower().endswith(('.tar.gz', '.zip', '.bin', '.safetensors')):
                return 'synced'
            else:
                return 'synced'
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"

@method_decorator(csrf_exempt, name='dispatch')
class PathCompareView(View):
    """路径对比详情"""
    
    @auth('model.storage.view')
    def get(self, request):
        """获取指定路径的本地和远程对比详情"""
        path = request.GET.get('path', '')
        
        try:
            # 获取本地信息
            local_info = self.get_local_path_info(path)
            
            # 获取远程信息
            remote_info = self.get_remote_path_info(path)
            
            # 生成对比结果
            compare_result = self.compare_path_info(local_info, remote_info, path)
            
            return JsonResponse({'data': compare_result})
            
        except Exception as e:
            print(f"路径对比失败: {e}")
            return JsonResponse({'data': {'error': str(e)}})
    
    def get_local_path_info(self, path):
        """获取本地路径信息"""
        import os
        
        try:
            if os.path.exists(path):
                stat = os.stat(path)
                is_dir = os.path.isdir(path)
                
                return {
                    'path': path,
                    'exists': True,
                    'type': 'folder' if is_dir else 'file',
                    'size': 'DIR' if is_dir else self.format_file_size(stat.st_size),
                    'lastModified': self.format_timestamp(stat.st_mtime),
                    'status': 'synced'
                }
            else:
                return {
                    'path': path,
                    'exists': False,
                    'type': 'unknown',
                    'size': '-',
                    'lastModified': '-',
                    'status': 'missing'
                }
        except Exception as e:
            print(f"获取本地信息失败: {e}")
            return {
                'path': path,
                'exists': False,
                'type': 'error',
                'size': '-',
                'lastModified': '-',
                'status': 'error'
            }
    
    def get_remote_path_info(self, path):
        """获取远程路径信息"""
        import requests
        from urllib.parse import urljoin
        
        base_url = "http://***********/GPU_MODEL_REPO/01.DEV/"
        auth = ('sys49169', 'Aa123,.,.') 
        
        try:
            # 构建远程URL
            if '/Model/' in path:
                model_name = path.split('/Model/')[-1].strip('/')
                remote_url = urljoin(base_url, f"Model/{model_name}/")
            elif '/Vendor/' in path:
                vendor_path = path.split('/Vendor/')[-1].strip('/')
                remote_url = urljoin(base_url, f"Vendor/{vendor_path}/")
            else:
                remote_url = base_url
            
            response = requests.get(remote_url, timeout=10, auth=auth)
            
            if response.status_code == 200:
                # 简单检查是否存在
                return {
                    'path': remote_url,
                    'exists': True,
                    'type': 'folder',  # 简化处理，假设都是目录
                    'size': 'DIR',
                    'lastModified': 'Unknown',
                    'status': 'synced'
                }
            else:
                return {
                    'path': remote_url,
                    'exists': False,
                    'type': 'unknown',
                    'size': '-',
                    'lastModified': '-',
                    'status': 'missing'
                }
                
        except Exception as e:
            print(f"获取远程信息失败: {e}")
            return {
                'path': path,
                'exists': False,
                'type': 'error',
                'size': '-',
                'lastModified': '-',
                'status': 'error'
            }
    
    def compare_path_info(self, local_info, remote_info, path):
        """对比本地和远程信息"""
        
        # 生成差异总结
        if not local_info['exists'] and not remote_info['exists']:
            diff_summary = "本地和远程都不存在此路径"
            status = 'missing'
        elif local_info['exists'] and not remote_info['exists']:
            diff_summary = "本地存在，远程缺失"
            status = 'added'
        elif not local_info['exists'] and remote_info['exists']:
            diff_summary = "远程存在，本地缺失"
            status = 'missing'
        elif local_info['exists'] and remote_info['exists']:
            # 都存在，比较详细信息
            if local_info['size'] != remote_info['size']:
                diff_summary = "文件大小不一致，可能已修改"
                status = 'modified'
            else:
                diff_summary = "本地和远程文件匹配"
                status = 'synced'
        else:
            diff_summary = "无法确定差异状态"
            status = 'conflict'
        
        return {
            'path': path,
            'local': local_info,
            'remote': remote_info,
            'diff_summary': diff_summary,
            'status': status,
            'compare_time': timezone.now().isoformat()
        }
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"
    
    def format_timestamp(self, timestamp):
        """格式化时间戳"""
        import datetime
        return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

@method_decorator(csrf_exempt, name='dispatch')
class FileTreeCompareView(View):
    """获取文件树状结构对比数据"""
    
    @auth('model.storage.view')
    def get(self, request):
        try:
            # 检查是否只需要加载第一级目录
            load_first_level = request.GET.get('first_level', 'false').lower() == 'true'
            
            if load_first_level:
                # 只加载第一级目录
                tree_data = self._build_first_level_tree()
            else:
                # 加载完整树结构
                tree_data = self._build_file_tree()
                
            return JsonResponse({'data': tree_data})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def _build_first_level_tree(self):
        """只构建第一级目录树结构，不获取子目录详情"""
        tree_data = []
        
        # 构建Model目录
        tree_data.append({
            'title': '📁 Model (加载中...)',
            'key': 'model_root',
            'icon': '📁',
            'data': {
                'name': 'Model',
                'path': '/HDD_Raid/SVN_MODEL_REPO/Model',
                'type': 'folder',
                'status': 'synced',
                'diff_summary': '点击展开查看模型目录'
            },
            'isLeaf': False,
            'children': []  # 空子节点，表示需要懒加载
        })
        
        # 构建Vendor目录
        vendor_list = ['AMD', 'Biren', 'Cambricon', 'Enflame', 'Hygon', 
                      'Iluvatar', 'Kunlunxin', 'MetaX', 'Moffett', 'Moore_Threads']
        
        tree_data.append({
            'title': f'📁 Vendor ({len(vendor_list)} 个厂商)',
            'key': 'vendor_root',
            'icon': '📁',
            'data': {
                'name': 'Vendor',
                'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor',
                'type': 'folder',
                'status': 'synced',
                'diff_summary': f'包含 {len(vendor_list)} 个厂商的测试文件'
            },
            'isLeaf': False,
            'children': []  # 空子节点，表示需要懒加载
        })
        
        return tree_data
    
    def _build_file_tree(self):
        """构建文件树结构 - 使用真实远程数据"""
        try:
            # 获取真实的远程文件数据
            remote_files = self.scan_remote_files()
            
            # 构建树结构
            tree_data = self._build_real_tree(remote_files)
            
            return tree_data
            
        except Exception as e:
            print(f"构建文件树失败，使用fallback数据: {e}")
            # 如果获取真实数据失败，返回基本结构
            return self._build_fallback_tree()
    
    def _build_real_tree(self, remote_files):
        """使用真实数据构建树结构"""
        tree_data = []
        
        # 按照目录分组
        model_files = [f for f in remote_files if f['name'] and not f['name'].startswith('Vendor/')]
        
        # 构建Model目录树
        if model_files:
            model_children = []
            for file_info in model_files:
                if file_info['type'] == 'folder' and '/' not in file_info['name']:
                    # 这是一个顶级模型目录
                    file_count = file_info.get('file_count', 0)
                    status = file_info.get('status', 'synced')
                    
                    # 根据状态选择图标
                    if status == 'missing':
                        icon = '❌'  # 空目录
                    elif status == 'partial':
                        icon = '⚠️'  # 不完整
                    else:
                        icon = '🤖'  # 正常模型
                    
                    model_children.append({
                        'title': f'{icon} {file_info["name"]}',
                        'key': f"model_{file_info['name']}",
                        'icon': icon,
                        'data': {
                            'name': file_info['name'],
                            'path': file_info['path'],
                            'type': 'folder',
                            'status': status,
                            'diff_summary': file_info.get('diff_summary', '远程模型目录'),
                            'file_count': file_count,
                            'size': file_info.get('size', 'Unknown')
                        },
                        'isLeaf': file_count == 0,
                        'children': [] if file_count == 0 else [
                            {
                                'title': f"📄 {file_count} 个文件",
                                'key': f"model_{file_info['name']}_files",
                                'icon': '📄',
                                'data': {
                                    'name': f"{file_count} 个文件",
                                    'type': 'info',
                                    'status': status,
                                    'diff_summary': '模型文件详情'
                                },
                                'isLeaf': True
                            }
                        ]
                    })
            
            # Model主目录
            total_models = len(model_children)
            empty_models = len([m for m in model_children if m['data'].get('file_count', 0) == 0])
            
            tree_data.append({
                'title': f'📁 Model ({total_models} 个模型)',
                'key': 'model_root',
                'icon': '📁',
                'data': {
                    'name': 'Model',
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Model',
                    'type': 'folder',
                    'status': 'partial' if empty_models > 0 else 'synced',
                    'diff_summary': f'包含 {total_models} 个模型目录' + (f'，其中 {empty_models} 个为空' if empty_models > 0 else '')
                },
                'isLeaf': False,
                'children': model_children
            })
        
        # 构建Vendor目录树（使用svn.md的信息作为补充）
        vendor_list = ['AMD', 'Biren', 'Cambricon', 'Enflame', 'Hygon', 
                      'Iluvatar', 'Kunlunxin', 'MetaX', 'Moffett', 'Moore_Threads']
        
        vendor_children = []
        for vendor in vendor_list:
            vendor_children.append({
                'title': f'🏢 {vendor}',
                'key': f"vendor_{vendor}",
                'icon': '🏢',
                'data': {
                    'name': vendor,
                    'path': f'/HDD_Raid/SVN_MODEL_REPO/Vendor/{vendor}',
                    'type': 'folder',
                    'status': 'synced',
                    'diff_summary': f'{vendor} 厂商测试文件'
                },
                'isLeaf': False,
                'children': [
                    {
                        'title': "💾 显卡型号和测试文件",
                        'key': f"vendor_{vendor}_cards",
                        'icon': '💾',
                        'data': {
                            'name': "显卡型号和测试文件",
                            'type': 'info',
                            'status': 'synced',
                            'diff_summary': '包含多种显卡型号的测试文件'
                        },
                        'isLeaf': True
                    }
                ]
            })
        
        # Vendor主目录
        tree_data.append({
            'title': f'📁 Vendor ({len(vendor_list)} 个厂商)',
            'key': 'vendor_root',
            'icon': '📁',
            'data': {
                'name': 'Vendor',
                'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor',
                'type': 'folder',
                'status': 'synced',
                'diff_summary': f'包含 {len(vendor_list)} 个厂商的测试文件'
            },
            'isLeaf': False,
            'children': vendor_children
        })
        
        return tree_data
    
    def _build_fallback_tree(self):
        """构建fallback树结构（当真实数据获取失败时使用）"""
        # 基于之前检查结果的已知模型列表
        model_list = [
            'Baichuan2-13B', 'Baichuan2-7B', 'ChatGLM3-6B', 'CodeGeeX2-6B', 
            'CodeLlama-7b-hf', 'DeepSeek-Coder-33B-Instruct', 'DeepSeek-V3-0324-P800',
            'GLM-4-9B-Chat', 'InternLM2-7B', 'Llama-2-13b-hf', 'Llama-2-7b-chat-hf',
            'Llama-3-8B-Instruct', 'Llama-3.1-8B-Instruct', 'Llama-3.2-1B-Instruct',
            'Llama-3.2-3B-Instruct', 'Meta-Llama-3-8B', 'Mistral-7B-Instruct-v0.2',
            'Qwen-7B', 'Qwen-VL-Chat', 'Qwen2-0.5B', 'Qwen2-1.5B', 'Qwen2-7B',
            'Qwen2-72B', 'Qwen2.5-0.5B', 'Qwen2.5-1.5B', 'Qwen2.5-14B', 'Qwen2.5-3B',
            'Qwen2.5-7B', 'Qwen2.5-32B', 'Qwen2.5-72B', 'Qwen2.5-Coder-1.5B-Instruct',
            'Qwen2.5-Coder-7B-Instruct', 'Qwen2.5-Math-1.5B-Instruct', 
            'Qwen2.5-Math-7B-Instruct', 'Qwen2.5-VL-2B-Instruct', 'Qwen2.5-VL-7B-Instruct',
            'Qwen2.5-VL-32B-Instruct', 'Qwen3-4B', 'Qwen3-7B', 'Yi-1.5-6B', 'Yi-1.5-9B',
            'Yi-34B', 'Yi-6B', 'Yi-9B', 'Yi-Coder-1.5B', 'Yi-Coder-9B', 'gemma-2-2b-it',
            'gemma-2-9b-it', 'gemma-2b-it', 'gemma-7b-it', 'glm-4-9b-chat', 'llama3-8b-chinese-chat-v3'
        ]
        
        # 空目录
        empty_models = ['Qwen2.5-VL-32B-Instruct']
        
        # 构建Model根节点
        model_children = []
        for model_name in model_list:
            # 判断模型状态
            if model_name in empty_models:
                status = 'missing'  # 空目录
                diff_summary = '远程模型目录为空'
                file_count = 0
                icon = '❌'
            else:
                status = 'synced'  # 正常状态
                diff_summary = '远程模型文件存在'
                file_count = 5  # 假设平均5个文件
                icon = '🤖'
            
            model_children.append({
                'title': f'{icon} {model_name}',
                'key': f"model_{model_name}",
                'icon': icon,
                'data': {
                    'name': model_name,
                    'path': f'/HDD_Raid/SVN_MODEL_REPO/Model/{model_name}',
                    'type': 'folder',
                    'status': status,
                    'diff_summary': diff_summary,
                    'file_count': file_count
                },
                'isLeaf': file_count == 0,
                'children': [] if file_count == 0 else [
                    {
                        'title': f"📄 {file_count} 个文件",
                        'key': f"model_{model_name}_files",
                        'icon': '📄',
                        'data': {
                            'name': f"{file_count} 个文件",
                            'type': 'info',
                            'status': status,
                            'diff_summary': '模型文件详情'
                        },
                        'isLeaf': True
                    }
                ]
            })
        
        tree_data = [
            {
                'title': f'📁 Model ({len(model_list)} 个模型)',
                                'key': 'model_root',
                'icon': '📁',
                'data': {
                    'name': 'Model',
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Model',
                    'type': 'folder',
                    'status': 'partial',
                    'diff_summary': f'包含 {len(model_list)} 个模型目录，其中 {len(empty_models)} 个为空'
                },
                'isLeaf': False,
                'children': model_children
            },
            {
                'title': f'📁 Vendor (10 个厂商)',
                'key': 'vendor_root',
                'icon': '📁',
                'data': {
                    'name': 'Vendor',
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor',
                    'type': 'folder',
                    'status': 'synced',
                    'diff_summary': '包含 10 个厂商的测试文件'
                },
                'isLeaf': False,
                'children': [
                    {
                        'title': '🏢 AMD',
                        'key': 'vendor_AMD',
                        'icon': '🏢',
                        'data': {
                            'name': 'AMD',
                            'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor/AMD',
                            'type': 'folder',
                            'status': 'synced',
                            'diff_summary': 'AMD 厂商测试文件'
                        },
                        'isLeaf': False,
                        'children': [
                            {
                                'title': "💾 显卡型号和测试文件",
                                'key': "vendor_AMD_cards",
                                'icon': '💾',
                                'data': {
                                    'name': "显卡型号和测试文件",
                                    'type': 'info',
                                    'status': 'synced',
                                    'diff_summary': '包含多种显卡型号的测试文件'
                                },
                                'isLeaf': True
                            }
                        ]
                    },
                    {
                        'title': '🏢 Biren',
                        'key': 'vendor_Biren',
                        'icon': '🏢',
                        'data': {
                            'name': 'Biren',
                            'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor/Biren',
                            'type': 'folder',
                            'status': 'synced',
                            'diff_summary': 'Biren 厂商测试文件'
                        },
                        'isLeaf': False,
                        'children': [
                            {
                                'title': "💾 显卡型号和测试文件",
                                'key': "vendor_Biren_cards",
                                'icon': '💾',
                                'data': {
                                    'name': "显卡型号和测试文件",
                                    'type': 'info',
                                    'status': 'synced',
                                    'diff_summary': '包含多种显卡型号的测试文件'
                                },
                                'isLeaf': True
                            }
                        ]
                    },
                    {
                        'title': '🏢 Kunlunxin',
                        'key': 'vendor_Kunlunxin',
                        'icon': '🏢',
                        'data': {
                            'name': 'Kunlunxin',
                            'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor/Kunlunxin',
                            'type': 'folder',
                            'status': 'synced',
                            'diff_summary': 'Kunlunxin 厂商测试文件'
                        },
                        'isLeaf': False,
                        'children': [
                            {
                                'title': "💾 显卡型号和测试文件",
                                'key': "vendor_Kunlunxin_cards",
                                'icon': '💾',
                                'data': {
                                    'name': "显卡型号和测试文件",
                                    'type': 'info',
                                    'status': 'synced',
                                    'diff_summary': '包含多种显卡型号的测试文件'
                                },
                                'isLeaf': True
                            }
                        ]
                    }
                ]
            }
        ]
        
        return tree_data
    
    def scan_remote_files(self):
        """扫描远程仓库文件列表"""
        import requests
        from urllib.parse import urljoin
        
        remote_url = "http://***********/GPU_MODEL_REPO/01.DEV/"
        # 使用SVN认证信息
        auth = ('sys49169', 'Aa123,.,.') 
        
        try:
            # 获取Model目录列表
            model_url = urljoin(remote_url, "Model/")
            response = requests.get(model_url, timeout=15, auth=auth)
            response.raise_for_status()
            
            # 解析HTML获取模型列表
            remote_models = self.parse_remote_html(response.text, model_url)
            
            # 获取每个模型的详细信息
            detailed_files = []
            for model in remote_models:
                if model['type'] == 'folder':
                    model_details = self.get_model_details(remote_url, model['name'], auth)
                    detailed_files.extend(model_details)
            
            return detailed_files
            
        except Exception as e:
            print(f"获取远程文件列表失败: {e}")
            return []
    
    def parse_remote_html(self, html_content, base_url):
        """解析远程目录的HTML内容"""
        import re
        files = []
        
        # 简单的正则表达式匹配文件链接
        link_pattern = r'<a href="([^"]+)"[^>]*>([^<]+)</a>'
        matches = re.findall(link_pattern, html_content)
        
        for href, text in matches:
            if href.startswith('..') or href.startswith('/'):
                continue
                
            is_dir = href.endswith('/')
            name = text.strip()
            if is_dir:
                name = name.rstrip('/')
            
            files.append({
                'path': f"{base_url}{href}",
                'name': name,
                'type': 'folder' if is_dir else 'file',
                'size': 'DIR' if is_dir else 'Unknown',
                'lastModified': 'Unknown',
                'source': 'remote'
            })
            
        return files
    
    def get_model_details(self, base_url, model_name, auth):
        """获取单个模型的详细文件信息"""
        import requests
        from urllib.parse import urljoin
        
        model_files = []
        try:
            model_url = urljoin(base_url, f"Model/{model_name}/")
            response = requests.get(model_url, timeout=10, auth=auth)
            response.raise_for_status()
            
            # 解析模型目录内容
            files_in_model = self.parse_remote_html(response.text, model_url)
            
            # 判断模型目录状态
            if len(files_in_model) == 0:
                # 空目录
                status = 'missing'
                diff_summary = '远程模型目录为空'
            elif any(f['name'] == 'md5sums.txt' for f in files_in_model):
                # 包含校验文件，认为是完整的
                status = 'synced'
                diff_summary = '远程模型文件完整'
            else:
                # 没有校验文件，可能不完整
                status = 'partial'
                diff_summary = '远程模型文件可能不完整（缺少md5sums.txt）'
            
            # 添加模型目录信息
            model_files.append({
                'path': model_url,
                'name': model_name,
                'type': 'folder',
                'size': f"{len(files_in_model)} items",
                'lastModified': 'Unknown',
                'status': status,
                'diff_summary': diff_summary,
                'source': 'remote',
                'file_count': len(files_in_model)
            })
            
        except Exception as e:
            print(f"获取模型 {model_name} 详情失败: {e}")
            # 添加错误信息
            model_files.append({
                'path': f"{base_url}Model/{model_name}/",
                'name': model_name,
                'type': 'folder',
                'size': 'Error',
                'lastModified': 'Unknown',
                'status': 'missing',
                'diff_summary': f'无法访问远程模型目录: {str(e)}',
                'source': 'remote',
                'file_count': 0
            })
            
        return model_files

# URL路由处理函数
@auth('model.storage.view')
def release_plan_detail(request, plan_id):
    """发布计划详情处理"""
    view = ReleasePlanView()
    if request.method == 'PUT':
        return view.put(request)
    elif request.method == 'DELETE':
        return view.delete(request)
    else:
        return JsonResponse({'error': '不支持的方法'}, status=405)

@method_decorator(csrf_exempt, name='dispatch')
class LoadModelDirectoryView(View):
    """懒加载Model目录下的子目录"""
    
    @auth('model.storage.view')
    def get(self, request):
        """获取Model目录下的子目录"""
        directory = request.GET.get('directory', 'Model')  # 默认加载Model目录
        
        try:
            if directory == 'Model':
                # 加载Model目录下的模型列表
                model_children = self.get_model_directories()
                return JsonResponse({'data': model_children})
            elif directory == 'Vendor':
                # 加载Vendor目录下的厂商列表
                vendor_children = self.get_vendor_directories()
                return JsonResponse({'data': vendor_children})
            else:
                return JsonResponse({'error': f'不支持的目录: {directory}'}, status=400)
        except Exception as e:
            print(f"加载目录失败: {e}")

            return JsonResponse({'error': str(e)}, status=500)
    
    def get_model_directories(self):
        """获取Model目录下的模型列表"""
       

        import os
        
        base_path = '/HDD_Raid/SVN_MODEL_REPO/Model'
        model_dirs = []
        
        try:
            if not os.path.exists(base_path):
                return []
                
            # 只获取直接子目录，不递归
            for item in os.listdir(base_path):
                item_path = os.path.join(base_path, item)
                
                if os.path.isdir(item_path):
                    # 快速检查是否有文件
                    has_files = False
                    file_count = 0
                    
                    try:
                        dir_items = os.listdir(item_path)
                        file_count = len(dir_items)
                        has_files = file_count > 0
                    except:
                        pass
                    
                    # 根据是否有文件设置图标和状态
                    if has_files:
                        icon = '🤖'  # 正常模型
                        status = 'synced'
                        diff_summary = f'模型目录，包含 {file_count} 个文件'
                    else:
                        icon = '❌'  # 空目录
                        status = 'missing'
                        diff_summary = '空模型目录'
                    
                    model_dirs.append({
                        'title': f'{icon} {item}',
                        'key': f"model_{item}",
                        'icon': icon,
                        'data': {
                            'name': item,
                            'path': item_path,
                            'type': 'folder',
                            'status': status,
                            'diff_summary': diff_summary,
                            'file_count': file_count,
                            'size': self.get_quick_dir_size(item_path)
                        },
                        'isLeaf': not has_files,
                        'children': [] if not has_files else None  # None表示需要懒加载
                    })
            
            # 按名称排序
            model_dirs.sort(key=lambda x: x['data']['name'].lower())
            
        except Exception as e:
            print(f"获取模型目录失败: {e}")
            
        return model_dirs
    
    def get_vendor_directories(self):
        """获取Vendor目录下的厂商列表"""
        import os
        
        base_path = '/HDD_Raid/SVN_MODEL_REPO/Vendor'
        vendor_dirs = []
        
        # 已知的厂商列表
        known_vendors = ['AMD', 'Biren', 'Cambricon', 'Enflame', 'Hygon', 
                         'Iluvatar', 'Kunlunxin', 'MetaX', 'Moffett', 'Moore_Threads']
        
        try:
            if not os.path.exists(base_path):
                # 如果目录不存在，使用已知厂商列表
                for vendor in known_vendors:
                    vendor_dirs.append({
                        'title': f'🏢 {vendor}',
                        'key': f"vendor_{vendor}",
                        'icon': '🏢',
                        'data': {
                            'name': vendor,
                            'path': f'{base_path}/{vendor}',
                            'type': 'folder',
                            'status': 'missing',
                            'diff_summary': f'{vendor} 厂商测试文件（目录不存在）'
                        },
                        'isLeaf': True
                    })
                return vendor_dirs
                
            # 获取实际目录
            for item in os.listdir(base_path):
                item_path = os.path.join(base_path, item)
                
                if os.path.isdir(item_path):
                    # 检查是否有子目录
                    has_subdirs = self.has_subdirectories(item_path)
                    
                    vendor_dirs.append({
                        'title': f'🏢 {item}',
                        'key': f"vendor_{item}",
                        'icon': '🏢',
                        'data': {
                            'name': item,
                            'path': item_path,
                            'type': 'folder',
                            'status': 'synced',
                            'diff_summary': f'{item} 厂商测试文件'
                        },
                        'isLeaf': not has_subdirs,
                        'children': [] if not has_subdirs else None  # None表示需要懒加载
                    })
            
            # 按名称排序
            vendor_dirs.sort(key=lambda x: x['data']['name'].lower())
            
        except Exception as e:
            print(f"获取厂商目录失败: {e}")
            
            # 如果出错，使用已知厂商列表
            for vendor in known_vendors:
                vendor_dirs.append({
                    'title': f'🏢 {vendor}',
                    'key': f"vendor_{vendor}",
                    'icon': '🏢',
                    'data': {
                        'name': vendor,
                        'path': f'{base_path}/{vendor}',
                        'type': 'folder',
                        'status': 'synced',
                        'diff_summary': f'{vendor} 厂商测试文件'
                    },
                    'isLeaf': False
                })
            
        return vendor_dirs
    
    def has_subdirectories(self, path):
        """快速检查目录是否有子目录"""
        try:
            for item in os.listdir(path):
                if os.path.isdir(os.path.join(path, item)):
                    return True
        except:
            pass
        return False
    
    def get_quick_dir_size(self, path):
        """快速获取目录大小（只计算直接文件）"""
        try:
            import os
            total = 0
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isfile(item_path):
                    try:
                        total += os.path.getsize(item_path)
                    except:
                        continue
            return self.format_file_size(total) if total > 0 else 'DIR'
        except:
            return 'DIR'
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"
    
    def get_file_time(self, path):
        """获取文件修改时间"""
        try:
            import os
            import datetime
            timestamp = os.path.getmtime(path)
            return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return 'Unknown'

