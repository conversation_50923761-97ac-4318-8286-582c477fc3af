import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { 
  Card, 
  Tree, 
  Spin, 
  Alert, 
  Tag, 
  Button, 
  Input, 
  Space,
  Divider,
  Row,
  Col
} from 'antd';
import { 
  FolderOutlined, 
  FileOutlined, 
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  MinusCircleOutlined,
  FullscreenOutlined,
  GlobalOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';
import { http } from '../../libs';
import styles from './FileDetailCompare.module.less';

const { Search } = Input;

export default function FileDetailCompare() {
  const [loading, setLoading] = useState(false);
  const [localTreeData, setLocalTreeData] = useState([]);
  const [remoteTreeData, setRemoteTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState(['local_root', 'remote_root']);
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const searchTimerRef = useRef(null);
  const [selectedPath, setSelectedPath] = useState('');
  const [compareDetails, setCompareDetails] = useState(null);
  const [error, setError] = useState(null);

  console.log('FileDetailCompare组件已渲染 - 全屏版本');
  
  // 获取配置参数 - 从URL参数或者默认值
  const getConfigFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      basePath: urlParams.get('basePath') || '/HDD_Raid/SVN_MODEL_REPO',
      remoteUrl: urlParams.get('remoteUrl') || 'http://10.63.30.93/GPU_MODEL_REPO/01.DEV/'
    };
  };
  
  const config = getConfigFromUrl();

  // 状态图标映射
  const statusIcons = {
    synced: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
    modified: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
    missing: <CloseCircleOutlined style={{ color: '#f5222d' }} />,
    added: <CheckCircleOutlined style={{ color: '#1890ff' }} />,
    conflict: <MinusCircleOutlined style={{ color: '#722ed1' }} />
  };

  // 状态标签映射
  const statusTags = {
    synced: <Tag color="success">已同步</Tag>,
    modified: <Tag color="warning">已修改</Tag>,
    missing: <Tag color="error">缺失</Tag>,
    added: <Tag color="processing">新增</Tag>,
    conflict: <Tag color="purple">冲突</Tag>
  };

  useEffect(() => {
    // 延迟加载，避免阻塞UI
    const timer = setTimeout(() => {
      loadInitialData();
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  const loadInitialData = useCallback(async () => {
    console.log('开始加载初始数据...');
    setLoading(true);
    setError(null);
    
    try {
      console.log('发起API请求...');
      
      // 先显示加载中的状态
      setLocalTreeData([{
        title: '📁 本地仓库 (加载中...)',
        key: 'local_loading',
        icon: <FolderOpenOutlined />,
        children: [{
          title: '⏳ 正在扫描本地文件...',
          key: 'local_loading_msg',
          isLeaf: true
        }]
      }]);
      
      setRemoteTreeData([{
        title: '🌐 远程仓库 (加载中...)',
        key: 'remote_loading',
        icon: <GlobalOutlined />,
        children: [{
          title: '⏳ 正在获取远程数据...',
          key: 'remote_loading_msg',
          isLeaf: true
        }]
      }]);

      // 渐进式加载：先加载本地数据（通常更快）
      console.log('加载本地数据...');
      try {
        const localResponse = await http.get('/api/model-storage/lazy-load-tree/', { 
          params: { path: config.basePath, depth: 1 } 
        });
        console.log('本地API响应:', localResponse);
        
        const localTree = buildLocalTree(localResponse);
        console.log('构建的本地树:', localTree);
        setLocalTreeData(localTree);
        console.log('本地数据设置完成');
      } catch (localError) {
        console.error('本地数据加载失败:', localError);
        setLocalTreeData([{
          title: '📁 本地仓库 (加载失败)',
          key: 'local_error',
          icon: <FolderOutlined />,
          children: [{
            title: `❌ ${localError.message || '加载失败，请重试'}`,
            key: 'local_error_msg',
            isLeaf: true
          }]
        }]);
      }

      // 然后加载远程数据（只加载第一级目录，提高速度）
      console.log('加载远程数据...');
      try {
        const remoteResponse = await http.get('/api/model-storage/file-tree-compare/', {
          params: { first_level: true }
        });
        console.log('远程API响应:', remoteResponse);
        
        const remoteTree = buildRemoteTree(remoteResponse);
        console.log('构建的远程树:', remoteTree);
        setRemoteTreeData(remoteTree);
        console.log('远程数据设置完成');
      } catch (remoteError) {
        console.error('远程数据加载失败:', remoteError);
        setRemoteTreeData([{
          title: '🌐 远程仓库 (加载失败)',
          key: 'remote_error',
          icon: <GlobalOutlined />,
          children: [{
            title: `❌ ${remoteError.message || '网络错误，请重试'}`,
            key: 'remote_error_msg',
            isLeaf: true
          }]
        }]);
      }

    } catch (error) {
      console.error('加载数据失败:', error);
      setError(error.message || '加载数据失败');
      
      // 显示错误信息但不阻塞界面
      setLocalTreeData([{
        title: '📁 本地仓库 (加载失败)',
        key: 'local_error',
        icon: <FolderOutlined />,
        children: [{
          title: '❌ 加载失败，请点击刷新重试',
          key: 'local_error_msg',
          isLeaf: true
        }]
      }]);
      
      setRemoteTreeData([{
        title: '🌐 远程仓库 (加载失败)',
        key: 'remote_error',
        icon: <GlobalOutlined />,
        children: [{
          title: '❌ 网络错误，请检查连接后重试',
          key: 'remote_error_msg',
          isLeaf: true
        }]
      }]);
    } finally {
      setLoading(false);
    }
  }, [config.basePath]);

  const buildLocalTree = (response) => {
    console.log('构建本地树，完整响应数据:', response);
    
    const baseTree = [{
      title: '📁 本地仓库',
      key: 'local_root',
      icon: <FolderOpenOutlined />,
      children: []
    }];

    // 处理不同的API响应格式
    let data = response;
    if (response && response.data) {
      data = response.data;
    }

    if (!data || !data.children || !Array.isArray(data.children)) {
      console.log('本地数据为空或格式不正确，使用默认结构');
      baseTree[0].children = [
        {
          title: '❌ 无数据或加载失败',
          key: 'local_no_data',
          icon: <FolderOutlined />,
          isLeaf: true,
          data: { name: '无数据', source: 'local' }
        }
      ];
      return baseTree;
    }

    // 限制初始加载的节点数量，避免一次性渲染过多
    const maxInitialNodes = 20;
    const limitedChildren = data.children.slice(0, maxInitialNodes);
    
    console.log(`本地数据总数: ${data.children.length}, 限制显示: ${limitedChildren.length}`);
    console.log('第一条本地数据示例:', limitedChildren[0]);

    baseTree[0].children = limitedChildren.map((item, index) => {
      const title = item.title || item.name || `本地项目${index + 1}`;
      const type = item.type || (item.isLeaf === false ? 'folder' : 'file');
      
      return {
        title: `${type === 'folder' ? '📁' : '📄'} ${title}`,
        key: `local_${item.key || index}`,
        icon: type === 'folder' ? <FolderOutlined /> : <FileOutlined />,
        isLeaf: item.isLeaf !== false && type !== 'folder',
        data: { 
          ...item, 
          ...(item.data || {}), 
          source: 'local',
          type: type 
        },
        // 初始不展开子节点，减少渲染负担
        children: (item.children && item.children.length > 0) || type === 'folder' ? [] : undefined
      };
    });

    // 如果有更多数据，添加"加载更多"节点
    if (data.children.length > maxInitialNodes) {
      baseTree[0].children.push({
        title: `📋 还有 ${data.children.length - maxInitialNodes} 个项目...`,
        key: 'local_load_more',
        icon: <FolderOutlined />,
        isLeaf: true,
        data: { name: '加载更多', source: 'local', isLoadMore: true }
      });
    }

    return baseTree;
  };

  const buildRemoteTree = (response) => {
    console.log('构建远程树，完整响应数据:', response);
    
    const baseTree = [{
      title: '🌐 远程仓库',
      key: 'remote_root', 
      icon: <GlobalOutlined />,
      children: []
    }];

    // 处理不同的API响应格式
    let data = response;
    if (response && response.data) {
      data = response.data;
    }
    
    // 如果data是数组，直接使用
    if (Array.isArray(data)) {
      console.log('API返回数组格式数据，长度:', data.length);
    } 
    // 如果data有children属性，使用children
    else if (data && Array.isArray(data.children)) {
      console.log('API返回对象格式数据，children长度:', data.children.length);
      data = data.children;
    }
    // 如果data是对象但不是数组，尝试转换为数组
    else if (data && typeof data === 'object' && !Array.isArray(data)) {
      console.log('API返回对象格式，尝试转换为数组:', Object.keys(data));
      data = Object.values(data).filter(item => item && typeof item === 'object');
    }
    else {
      console.log('远程数据格式不正确，使用默认结构');
      data = [];
    }

    if (!Array.isArray(data) || data.length === 0) {
      console.log('远程数据为空，使用默认结构');
      baseTree[0].children = [
        {
          title: '❌ 无数据或加载失败',
          key: 'remote_no_data',
          icon: <FolderOutlined />,
          isLeaf: true,
          data: { name: '无数据', source: 'remote' }
        }
      ];
      return baseTree;
    }

    // 限制初始加载的节点数量
    const maxInitialNodes = 20;
    const limitedData = data.slice(0, maxInitialNodes);
    
    console.log(`远程数据总数: ${data.length}, 限制显示: ${limitedData.length}`);
    console.log('第一条数据示例:', limitedData[0]);

    baseTree[0].children = limitedData.map((item, index) => {
      // 处理不同的数据格式
      const title = item.title || item.name || item.path || `项目${index + 1}`;
      const type = item.type || (item.isLeaf === false ? 'folder' : 'file');
      const status = item.status || item.data?.status || 'unknown';
      
      return {
        title: (
          <span style={{ display: 'inline-flex', alignItems: 'center' }}>
            {statusIcons[status] || (type === 'folder' ? '📁' : '📄')}
            <span style={{ marginLeft: 4 }}>{title}</span>
          </span>
        ),
        key: `remote_${item.key || item.path || index}`,
        icon: type === 'folder' ? <FolderOutlined /> : <FileOutlined />,
        isLeaf: item.isLeaf !== false && type !== 'folder',
        data: { 
          ...item, 
          ...(item.data || {}), 
          source: 'remote',
          type: type,
          status: status 
        },
        // 初始不展开子节点
        children: (item.children && item.children.length > 0) || type === 'folder' ? [] : undefined
      };
    });

    // 如果有更多数据，添加"加载更多"节点
    if (data.length > maxInitialNodes) {
      baseTree[0].children.push({
        title: `📋 还有 ${data.length - maxInitialNodes} 个项目...`,
        key: 'remote_load_more',
        icon: <FolderOutlined />,
        isLeaf: true,
        data: { name: '加载更多', source: 'remote', isLoadMore: true }
      });
    }

    return baseTree;
  };

  const onLoadData = async (treeNode) => {
    const { key, data } = treeNode;
    
    if (!data?.path || treeNode.children) {
      return;
    }

    try {
      let response;
      if (key === 'model_root') {
        // 加载Model目录下的所有模型
        response = await http.get('/api/model-storage/load-directory/', {
          params: { directory: 'Model' }
        });
        
        const children = response.data?.map(item => ({
          title: item.title,
          key: item.key,
          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,
          isLeaf: item.isLeaf,
          data: { ...item.data, source: 'remote' },
          children: item.children
        })) || [];
        
        treeNode.children = children;
      } else if (key === 'vendor_root') {
        // 加载Vendor目录下的所有厂商
        response = await http.get('/api/model-storage/load-directory/', {
          params: { directory: 'Vendor' }
        });
        
        const children = response.data?.map(item => ({
          title: item.title,
          key: item.key,
          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,
          isLeaf: item.isLeaf,
          data: { ...item.data, source: 'remote' },
          children: item.children
        })) || [];
        
        treeNode.children = children;
      } else if (key.startsWith('local_')) {
        // 加载本地子目录
        response = await http.get('/api/model-storage/lazy-load-tree/', {
          params: { path: data.path, depth: 1 }
        });
        
        const children = response.data.children?.map(item => ({
          title: `${item.type === 'folder' ? '📁' : '📄'} ${item.title}`,
          key: `local_${item.key}`,
          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,
          isLeaf: item.isLeaf,
          data: { ...item, source: 'local' }
        })) || [];
        
        treeNode.children = children;
      } else if (key.startsWith('remote_')) {
        // 加载远程子目录
        response = await http.get('/api/model-storage/remote-tree-detail/', {
          params: { path: data.path }
        });
        
        const children = response.data?.map(item => ({
          title: `${statusIcons[item.status] || (item.type === 'folder' ? '📁' : '📄')} ${item.name}`,
          key: `remote_${item.path.replace(/[^a-zA-Z0-9]/g, '_')}`,
          icon: item.type === 'folder' ? <FolderOutlined /> : <FileOutlined />,
          isLeaf: item.type === 'file',
          data: { ...item, source: 'remote' }
        })) || [];
        
        treeNode.children = children;
      }

      // 更新树数据
      if (key.startsWith('local_')) {
        setLocalTreeData([...localTreeData]);
      } else {
        setRemoteTreeData([...remoteTreeData]);
      }

    } catch (error) {
      console.error('加载子节点失败:', error);
    }
  };

  const onSelect = async (selectedKeys, info) => {
    if (!selectedKeys.length) return;
    
    const { node } = info;
    const { data } = node;
    
    setSelectedPath(data?.path || '');
    
    // 如果选择的是文件夹，加载详细对比信息
    if (data?.type === 'folder' && data?.path) {
      try {
        const response = await http.get('/api/model-storage/path-compare/', {
          params: { path: data.path }
        });
        setCompareDetails(response.data);
      } catch (error) {
        console.error('加载对比详情失败:', error);
      }
    }
  };

  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
  };

  const handleRefresh = () => {
    loadInitialData();
  };

  const handleSync = async (path) => {
    try {
      await http.post('/api/model-storage/sync-single-file/', { file_path: path });
      // 刷新数据
      loadInitialData();
    } catch (error) {
      console.error('同步失败:', error);
    }
  };

  const renderCompareDetails = () => {
    if (!compareDetails) return null;

    return (
      <Card title="详细对比信息" size="small" className={styles.compareDetails}>
        <Row gutter={16}>
          <Col span={12}>
            <Card size="small" title="本地状态" bordered={false}>
              <div className={styles.statusInfo}>
                <p><strong>路径:</strong> {compareDetails.local?.path || '不存在'}</p>
                <p><strong>大小:</strong> {compareDetails.local?.size || '-'}</p>
                <p><strong>修改时间:</strong> {compareDetails.local?.lastModified || '-'}</p>
                <p><strong>状态:</strong> {statusTags[compareDetails.local?.status] || statusTags.missing}</p>
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title="远程状态" bordered={false}>
              <div className={styles.statusInfo}>
                <p><strong>路径:</strong> {compareDetails.remote?.path || '不存在'}</p>
                <p><strong>大小:</strong> {compareDetails.remote?.size || '-'}</p>
                <p><strong>修改时间:</strong> {compareDetails.remote?.lastModified || '-'}</p>
                <p><strong>状态:</strong> {statusTags[compareDetails.remote?.status] || statusTags.missing}</p>
              </div>
            </Card>
          </Col>
        </Row>
        <Divider />
        <div className={styles.diffSummary}>
          <h4>差异总结:</h4>
          <p>{compareDetails.diff_summary}</p>
          {compareDetails.local?.status !== 'synced' && (
            <Button 
              type="primary" 
              icon={<SyncOutlined />}
              onClick={() => handleSync(selectedPath)}
            >
              同步文件
            </Button>
          )}
        </div>
      </Card>
    );
  };

  const filterTreeData = (data, searchValue) => {
    if (!searchValue) return data;
    
    return data.map(node => {
      const isMatch = node.title.toLowerCase().includes(searchValue.toLowerCase());
      const filteredChildren = node.children ? filterTreeData(node.children, searchValue) : [];
      
      if (isMatch || filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren
        };
      }
      return null;
    }).filter(Boolean);
  };

  // 防抖搜索效果
  useEffect(() => {
    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }
    
    searchTimerRef.current = setTimeout(() => {
      setDebouncedSearchValue(searchValue);
    }, 300); // 300ms防抖
    
    return () => {
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }
    };
  }, [searchValue]);

  // 使用 useMemo 缓存过滤结果，避免每次渲染都重新计算
  const filteredLocalTree = useMemo(() => {
    console.log('重新计算本地树过滤结果');
    return filterTreeData(localTreeData, debouncedSearchValue);
  }, [localTreeData, debouncedSearchValue]);
  
  const filteredRemoteTree = useMemo(() => {
    console.log('重新计算远程树过滤结果');
    return filterTreeData(remoteTreeData, debouncedSearchValue);
  }, [remoteTreeData, debouncedSearchValue]);

  return (
    <div className={styles.fileDetailCompareFullscreen}>
      <Alert
        message={loading ? "🔄 正在加载数据..." : "🚀 文件详细对比功能 - 全屏版本"}
        description={loading ? 
          "正在获取本地和远程仓库数据，请稍候..." : 
          `对比: ${config.basePath} ↔ ${config.remoteUrl}`
        }
        type={loading ? "warning" : "info"}
        style={{ marginBottom: 16 }}
        showIcon
        action={
          <Space>
            {!loading && (
              <Button 
                size="small" 
                icon={<FullscreenOutlined />}
                onClick={() => document.documentElement.requestFullscreen?.()}
              >
                全屏
              </Button>
            )}
          </Space>
        }
      />
      
      <Card 
        title={
          <Space>
            <span>文件详细对比</span>
            {loading && <Spin size="small" />}
          </Space>
        }
        extra={
          <Space>
            <Search
              placeholder="搜索文件或目录"
              allowClear
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              style={{ width: 250 }}
              prefix={<SearchOutlined />}
            />
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
        className={styles.fullscreenCard}
        bodyStyle={{ height: 'calc(100vh - 200px)', padding: '16px' }}
      >
        {error && (
          <Alert
            message="加载失败"
            description={error}
            type="error"
            style={{ marginBottom: 16 }}
            action={
              <Button 
                size="small" 
                type="primary" 
                onClick={loadInitialData}
              >
                重试
              </Button>
            }
          />
        )}
        
        <Row gutter={16} style={{ height: '100%' }}>
          <Col span={12}>
            <Card 
              size="small" 
              title={
                <Space>
                  <FolderOpenOutlined />
                  <span>本地仓库</span>
                  <Tag color="blue">{config.basePath}</Tag>
                </Space>
              }
              bordered={false}
              className={styles.treeCard}
              bodyStyle={{ height: 'calc(100vh - 300px)', overflow: 'auto' }}
            >
              <Spin spinning={loading}>
                <Tree
                  loadData={onLoadData}
                  treeData={filteredLocalTree}
                  onSelect={onSelect}
                  onExpand={onExpand}
                  expandedKeys={expandedKeys.filter(key => key.startsWith('local_'))}
                  showIcon
                  height="100%"
                  className={styles.localTree}
                />
              </Spin>
            </Card>
          </Col>
          <Col span={12}>
            <Card 
              size="small" 
              title={
                <Space>
                  <GlobalOutlined />
                  <span>远程仓库</span>
                  <Tag color="orange">{config.remoteUrl}</Tag>
                </Space>
              }
              bordered={false}
              className={styles.treeCard}
              bodyStyle={{ height: 'calc(100vh - 300px)', overflow: 'auto' }}
            >
              <Spin spinning={loading}>
                <Tree
                  loadData={onLoadData}
                  treeData={filteredRemoteTree}
                  onSelect={onSelect}
                  onExpand={onExpand}
                  expandedKeys={expandedKeys.filter(key => key.startsWith('remote_'))}
                  showIcon
                  height="100%"
                  className={styles.remoteTree}
                />
              </Spin>
            </Card>
          </Col>
        </Row>
        
        {selectedPath && (
          <div className={styles.selectedPath}>
            <Alert
              message={`已选择: ${selectedPath}`}
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          </div>
        )}
        
        {renderCompareDetails()}
      </Card>
    </div>
  );
} 